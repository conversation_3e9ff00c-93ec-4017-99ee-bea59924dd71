**BeautyHub — 04. Technical Architecture & Data Model **

---

**1. Purpose:**
Documenting the Supabase-first technical architecture and data model for BeautyHub MVP, covering both beauty technicians and customer journeys. This version highlights how Supabase services (Auth, Database, Storage, Functions) replace or complement traditional backend components.

---

**2. Technical Architecture:**

**2.1 Overview:**

* **Frontend:**

  * Mobile-first React Native application for iOS and Android.
  * State management using Redux or Zustand.
  * Navigation with React Navigation for stack and tab flows.
  * Push notifications via Firebase Cloud Messaging.
  * Accessibility support and dark mode toggle.

* **Backend / Services:**

  * **Supabase (Postgres + API + Auth + Storage).**
  * Authentication managed by <PERSON>pa<PERSON> Auth (email/password, OTP, Google, Apple).
  * Row-Level Security (RLS) policies enforce data access rules between customers, techs, and admins.
  * Edge Functions (serverless) used for custom logic:

    * Stripe webhooks (deposits, payouts, refunds).
    * Auto-release deposits after service completion.
    * Automated booking reminders.
  * Role-based access via Supabase Auth roles & claims.

* **Database:**

  * Supabase-managed PostgreSQL relational database.
  * Realtime subscriptions for bookings, availability, notifications.
  * Postgres Row-Level Security to separate customer/technician/admin data.

* **Storage:**

  * Supabase Storage for profile images, service images, and certifications.
  * Public/private bucket policies (e.g., certificates are private, service images public).

* **Integrations:**

  * **Stripe Connect (GBP £)** for deposits, payouts, and refunds.
  * Google Maps API for geolocation, distance calculation, and clustering.
  * SendGrid for transactional + marketing emails.
  * Firebase Cloud Messaging (FCM) for push notifications.

---

**3. Data Model (Postgres Schema):**

**3.1 Users Table:**

* user\_id (UUID, PK — from Supabase Auth)
* role (ENUM: tech, customer, admin)
* name
* email
* phone\_number
* profile\_picture\_url
* language\_preference
* timezone
* created\_at, updated\_at

**3.2 BeautyTechnicians Table:**

* tech\_id (PK, FK to Users.user\_id)
* studio\_name
* location (lat, lng)
* header\_image\_url
* bio
* social\_links JSONB
* working\_hours JSONB
* availability\_status (enum: available, unavailable)
* verification\_status (enum: unverified, verified, certified)
* certifications JSONB (file references in Supabase Storage)
* created\_at, updated\_at

**3.3 Services Table:**

* service\_id (UUID, PK)
* tech\_id (FK to BeautyTechnicians.tech\_id)
* name
* description
* category
* duration\_minutes
* price\_pence (integer, stored in pence but shown as £ to users)
* deposit\_pence (integer)
* add\_ons JSONB
* faqs JSONB
* images JSONB (Supabase Storage paths)
* video\_urls JSONB
* is\_active BOOLEAN
* created\_at, updated\_at

**3.4 Bookings Table:**

* booking\_id (UUID, PK)
* customer\_id (FK to Users.user\_id)
* service\_id (FK to Services.service\_id)
* tech\_id (FK to BeautyTechnicians.tech\_id)
* status (enum: pending, confirmed, completed, cancelled, no\_show)
* appointment\_time TIMESTAMP
* deposit\_paid BOOLEAN
* auto\_release\_time TIMESTAMP
* total\_amount\_pence (integer)
* payment\_status (enum: pending, paid, refunded)
* created\_at, updated\_at

**3.5 Reviews Table:**

* review\_id (UUID, PK)
* booking\_id (FK to Bookings.booking\_id)
* reviewer\_id (FK to Users.user\_id)
* reviewee\_id (FK to Users.user\_id)
* rating (1–5)
* comment TEXT
* media JSONB (images/videos in Storage)
* created\_at, updated\_at

**3.6 Notifications Table:**

* notification\_id (UUID, PK)
* user\_id (FK to Users.user\_id)
* type (enum: booking, cancellation, reminder, marketing)
* message TEXT
* read\_status BOOLEAN
* created\_at

**3.7 Account Settings Table:**

* settings\_id (UUID, PK)
* user\_id (FK to Users.user\_id)
* linked\_devices JSONB
* communication\_preferences JSONB
* privacy\_settings JSONB
* dark\_mode BOOLEAN
* accessibility\_options JSONB
* created\_at, updated\_at

**3.8 Payment Accounts Table:**

* payment\_id (UUID, PK)
* tech\_id (FK to BeautyTechnicians.tech\_id)
* stripe\_account\_id (for payouts)
* payout\_method (enum: instant, weekly, monthly)
* created\_at, updated\_at

**3.9 Audit Logs Table:**

* log\_id (UUID, PK)
* user\_id (FK to Users.user\_id)
* action\_type
* action\_details JSONB
* created\_at

---

**4. API & Functions:**

* Supabase auto-generates REST + GraphQL APIs for all tables.
* Edge Functions:

  * **/stripe/webhook** → handle payments, refunds, deposit release.
  * **/booking/reminder** → send reminders via FCM + SendGrid.
  * **/booking/auto\_release** → trigger deposit release.
  * **/admin/verify\_tech** → update verification status.

---

**5. Security & Compliance:**

* GDPR & CCPA compliant with Supabase storage + database.
* Supabase RLS (Row-Level Security) ensures users can only access their own data.
* Encrypted sensitive fields (Stripe IDs, payment details).
* Audit logs for admin actions.
* Optional 2FA via Supabase Auth.

---

**6. Scalability Considerations:**

* Supabase handles horizontal scaling of Postgres + storage.
* Realtime subscriptions for bookings and notifications.
* CDN-backed media delivery.
* Background jobs via Supabase Scheduled Functions (cron-style).

---

**7. Future Enhancements:**

* Multi-language support (Supabase i18n table + frontend translation).
* AI-powered recommendations for services.
* Advanced dashboards for tech revenue + KPIs.
* Loyalty & referral system stored in Supabase tables.

---

**Version:** v1.0 (Supabase Edition)
**Date:** 17/08/2025
