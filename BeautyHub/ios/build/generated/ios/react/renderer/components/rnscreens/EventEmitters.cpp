
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterCpp.js
 */

#include <react/renderer/components/rnscreens/EventEmitters.h>


namespace facebook::react {

void RNSBottomTabsEventEmitter::onNativeFocusChange(OnNativeFocusChange event) const {
  dispatchEvent("nativeFocusChange", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "tabKey", event.tabKey);
    return payload;
  });
}


void RNSBottomTabsScreenEventEmitter::onLifecycleStateChange(OnLifecycleStateChange event) const {
  dispatchEvent("lifecycleStateChange", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "previousState", event.previousState);
payload.setProperty(runtime, "newState", event.newState);
    return payload;
  });
}


void RNSBottomTabsScreenEventEmitter::onWillAppear(OnWillAppear event) const {
  dispatchEvent("willAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSBottomTabsScreenEventEmitter::onDidAppear(OnDidAppear event) const {
  dispatchEvent("didAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSBottomTabsScreenEventEmitter::onWillDisappear(OnWillDisappear event) const {
  dispatchEvent("willDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSBottomTabsScreenEventEmitter::onDidDisappear(OnDidDisappear event) const {
  dispatchEvent("didDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}




void RNSSplitViewHostEventEmitter::onCollapse(OnCollapse event) const {
  dispatchEvent("collapse", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSplitViewHostEventEmitter::onDisplayModeWillChange(OnDisplayModeWillChange event) const {
  dispatchEvent("displayModeWillChange", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "currentDisplayMode", event.currentDisplayMode);
payload.setProperty(runtime, "nextDisplayMode", event.nextDisplayMode);
    return payload;
  });
}


void RNSSplitViewHostEventEmitter::onExpand(OnExpand event) const {
  dispatchEvent("expand", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSplitViewHostEventEmitter::onInspectorHide(OnInspectorHide event) const {
  dispatchEvent("inspectorHide", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSplitViewScreenEventEmitter::onWillAppear(OnWillAppear event) const {
  dispatchEvent("willAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSplitViewScreenEventEmitter::onDidAppear(OnDidAppear event) const {
  dispatchEvent("didAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSplitViewScreenEventEmitter::onWillDisappear(OnWillDisappear event) const {
  dispatchEvent("willDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSplitViewScreenEventEmitter::onDidDisappear(OnDidDisappear event) const {
  dispatchEvent("didDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSStackScreenEventEmitter::onWillAppear(OnWillAppear event) const {
  dispatchEvent("willAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSStackScreenEventEmitter::onDidAppear(OnDidAppear event) const {
  dispatchEvent("didAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSStackScreenEventEmitter::onWillDisappear(OnWillDisappear event) const {
  dispatchEvent("willDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSStackScreenEventEmitter::onDidDisappear(OnDidDisappear event) const {
  dispatchEvent("didDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onAppear(OnAppear event) const {
  dispatchEvent("appear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onDisappear(OnDisappear event) const {
  dispatchEvent("disappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onDismissed(OnDismissed event) const {
  dispatchEvent("dismissed", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "dismissCount", event.dismissCount);
    return payload;
  });
}


void RNSModalScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled event) const {
  dispatchEvent("nativeDismissCancelled", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "dismissCount", event.dismissCount);
    return payload;
  });
}


void RNSModalScreenEventEmitter::onWillAppear(OnWillAppear event) const {
  dispatchEvent("willAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onWillDisappear(OnWillDisappear event) const {
  dispatchEvent("willDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange event) const {
  dispatchEvent("headerHeightChange", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "headerHeight", event.headerHeight);
    return payload;
  });
}


void RNSModalScreenEventEmitter::onTransitionProgress(OnTransitionProgress event) const {
  dispatchEvent("transitionProgress", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "progress", event.progress);
payload.setProperty(runtime, "closing", event.closing);
payload.setProperty(runtime, "goingForward", event.goingForward);
    return payload;
  });
}


void RNSModalScreenEventEmitter::onGestureCancel(OnGestureCancel event) const {
  dispatchEvent("gestureCancel", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked event) const {
  dispatchEvent("headerBackButtonClicked", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSModalScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged event) const {
  dispatchEvent("sheetDetentChanged", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "index", event.index);
payload.setProperty(runtime, "isStable", event.isStable);
    return payload;
  });
}





void RNSScreenEventEmitter::onAppear(OnAppear event) const {
  dispatchEvent("appear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenEventEmitter::onDisappear(OnDisappear event) const {
  dispatchEvent("disappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenEventEmitter::onDismissed(OnDismissed event) const {
  dispatchEvent("dismissed", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "dismissCount", event.dismissCount);
    return payload;
  });
}


void RNSScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled event) const {
  dispatchEvent("nativeDismissCancelled", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "dismissCount", event.dismissCount);
    return payload;
  });
}


void RNSScreenEventEmitter::onWillAppear(OnWillAppear event) const {
  dispatchEvent("willAppear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenEventEmitter::onWillDisappear(OnWillDisappear event) const {
  dispatchEvent("willDisappear", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange event) const {
  dispatchEvent("headerHeightChange", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "headerHeight", event.headerHeight);
    return payload;
  });
}


void RNSScreenEventEmitter::onTransitionProgress(OnTransitionProgress event) const {
  dispatchEvent("transitionProgress", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "progress", event.progress);
payload.setProperty(runtime, "closing", event.closing);
payload.setProperty(runtime, "goingForward", event.goingForward);
    return payload;
  });
}


void RNSScreenEventEmitter::onGestureCancel(OnGestureCancel event) const {
  dispatchEvent("gestureCancel", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked event) const {
  dispatchEvent("headerBackButtonClicked", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged event) const {
  dispatchEvent("sheetDetentChanged", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "index", event.index);
payload.setProperty(runtime, "isStable", event.isStable);
    return payload;
  });
}



void RNSScreenStackHeaderConfigEventEmitter::onAttached(OnAttached event) const {
  dispatchEvent("attached", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSScreenStackHeaderConfigEventEmitter::onDetached(OnDetached event) const {
  dispatchEvent("detached", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}



void RNSScreenStackEventEmitter::onFinishTransitioning(OnFinishTransitioning event) const {
  dispatchEvent("finishTransitioning", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSearchBarEventEmitter::onSearchFocus(OnSearchFocus event) const {
  dispatchEvent("searchFocus", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSearchBarEventEmitter::onSearchBlur(OnSearchBlur event) const {
  dispatchEvent("searchBlur", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSearchBarEventEmitter::onSearchButtonPress(OnSearchButtonPress event) const {
  dispatchEvent("searchButtonPress", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "text", event.text);
    return payload;
  });
}


void RNSSearchBarEventEmitter::onCancelButtonPress(OnCancelButtonPress event) const {
  dispatchEvent("cancelButtonPress", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSearchBarEventEmitter::onChangeText(OnChangeText event) const {
  dispatchEvent("changeText", [event=std::move(event)](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    payload.setProperty(runtime, "text", event.text);
    return payload;
  });
}


void RNSSearchBarEventEmitter::onClose(OnClose event) const {
  dispatchEvent("close", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}


void RNSSearchBarEventEmitter::onOpen(OnOpen event) const {
  dispatchEvent("open", [](jsi::Runtime &runtime) {
    auto payload = jsi::Object(runtime);
    
    return payload;
  });
}

} // namespace facebook::react
