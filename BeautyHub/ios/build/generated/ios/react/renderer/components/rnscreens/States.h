/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateStateH.js
 */
#pragma once

#include <react/renderer/core/StateData.h>
#ifdef RN_SERIALIZABLE_STATE
#include <folly/dynamic.h>
#endif

namespace facebook::react {

using RNSBottomTabsScreenState = StateData;

using RNSScreenStackHostState = StateData;

using RNSSplitViewHostState = StateData;

using RNSStackScreenState = StateData;

using RNSScreenContainerState = StateData;

using RNSScreenContentWrapperState = StateData;

using RNSScreenFooterState = StateData;

using RNSScreenNavigationContainerState = StateData;

using RNSScreenStackState = StateData;

using RNSSearchBarState = StateData;

} // namespace facebook::react