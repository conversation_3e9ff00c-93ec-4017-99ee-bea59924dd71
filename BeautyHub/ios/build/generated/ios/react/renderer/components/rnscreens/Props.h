
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <react/renderer/graphics/Color.h>
#include <react/renderer/imagemanager/primitives.h>
#include <vector>

namespace facebook::react {

enum class RNSBottomTabsTabBarBlurEffect { None, SystemDefault, ExtraLight, Light, Dark, Regular, Prominent, SystemUltraThinMaterial, SystemThinMaterial, SystemMaterial, SystemThickMaterial, SystemChromeMaterial, SystemUltraThinMaterialLight, SystemThinMaterialLight, SystemMaterialLight, SystemThickMaterialLight, SystemChromeMaterialLight, SystemUltraThinMaterialDark, SystemThinMaterialDark, SystemMaterialDark, SystemThickMaterialDark, SystemChromeMaterialDark };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsTabBarBlurEffect &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNSBottomTabsTabBarBlurEffect::None; return; }
  if (string == "systemDefault") { result = RNSBottomTabsTabBarBlurEffect::SystemDefault; return; }
  if (string == "extraLight") { result = RNSBottomTabsTabBarBlurEffect::ExtraLight; return; }
  if (string == "light") { result = RNSBottomTabsTabBarBlurEffect::Light; return; }
  if (string == "dark") { result = RNSBottomTabsTabBarBlurEffect::Dark; return; }
  if (string == "regular") { result = RNSBottomTabsTabBarBlurEffect::Regular; return; }
  if (string == "prominent") { result = RNSBottomTabsTabBarBlurEffect::Prominent; return; }
  if (string == "systemUltraThinMaterial") { result = RNSBottomTabsTabBarBlurEffect::SystemUltraThinMaterial; return; }
  if (string == "systemThinMaterial") { result = RNSBottomTabsTabBarBlurEffect::SystemThinMaterial; return; }
  if (string == "systemMaterial") { result = RNSBottomTabsTabBarBlurEffect::SystemMaterial; return; }
  if (string == "systemThickMaterial") { result = RNSBottomTabsTabBarBlurEffect::SystemThickMaterial; return; }
  if (string == "systemChromeMaterial") { result = RNSBottomTabsTabBarBlurEffect::SystemChromeMaterial; return; }
  if (string == "systemUltraThinMaterialLight") { result = RNSBottomTabsTabBarBlurEffect::SystemUltraThinMaterialLight; return; }
  if (string == "systemThinMaterialLight") { result = RNSBottomTabsTabBarBlurEffect::SystemThinMaterialLight; return; }
  if (string == "systemMaterialLight") { result = RNSBottomTabsTabBarBlurEffect::SystemMaterialLight; return; }
  if (string == "systemThickMaterialLight") { result = RNSBottomTabsTabBarBlurEffect::SystemThickMaterialLight; return; }
  if (string == "systemChromeMaterialLight") { result = RNSBottomTabsTabBarBlurEffect::SystemChromeMaterialLight; return; }
  if (string == "systemUltraThinMaterialDark") { result = RNSBottomTabsTabBarBlurEffect::SystemUltraThinMaterialDark; return; }
  if (string == "systemThinMaterialDark") { result = RNSBottomTabsTabBarBlurEffect::SystemThinMaterialDark; return; }
  if (string == "systemMaterialDark") { result = RNSBottomTabsTabBarBlurEffect::SystemMaterialDark; return; }
  if (string == "systemThickMaterialDark") { result = RNSBottomTabsTabBarBlurEffect::SystemThickMaterialDark; return; }
  if (string == "systemChromeMaterialDark") { result = RNSBottomTabsTabBarBlurEffect::SystemChromeMaterialDark; return; }
  abort();
}

static inline std::string toString(const RNSBottomTabsTabBarBlurEffect &value) {
  switch (value) {
    case RNSBottomTabsTabBarBlurEffect::None: return "none";
    case RNSBottomTabsTabBarBlurEffect::SystemDefault: return "systemDefault";
    case RNSBottomTabsTabBarBlurEffect::ExtraLight: return "extraLight";
    case RNSBottomTabsTabBarBlurEffect::Light: return "light";
    case RNSBottomTabsTabBarBlurEffect::Dark: return "dark";
    case RNSBottomTabsTabBarBlurEffect::Regular: return "regular";
    case RNSBottomTabsTabBarBlurEffect::Prominent: return "prominent";
    case RNSBottomTabsTabBarBlurEffect::SystemUltraThinMaterial: return "systemUltraThinMaterial";
    case RNSBottomTabsTabBarBlurEffect::SystemThinMaterial: return "systemThinMaterial";
    case RNSBottomTabsTabBarBlurEffect::SystemMaterial: return "systemMaterial";
    case RNSBottomTabsTabBarBlurEffect::SystemThickMaterial: return "systemThickMaterial";
    case RNSBottomTabsTabBarBlurEffect::SystemChromeMaterial: return "systemChromeMaterial";
    case RNSBottomTabsTabBarBlurEffect::SystemUltraThinMaterialLight: return "systemUltraThinMaterialLight";
    case RNSBottomTabsTabBarBlurEffect::SystemThinMaterialLight: return "systemThinMaterialLight";
    case RNSBottomTabsTabBarBlurEffect::SystemMaterialLight: return "systemMaterialLight";
    case RNSBottomTabsTabBarBlurEffect::SystemThickMaterialLight: return "systemThickMaterialLight";
    case RNSBottomTabsTabBarBlurEffect::SystemChromeMaterialLight: return "systemChromeMaterialLight";
    case RNSBottomTabsTabBarBlurEffect::SystemUltraThinMaterialDark: return "systemUltraThinMaterialDark";
    case RNSBottomTabsTabBarBlurEffect::SystemThinMaterialDark: return "systemThinMaterialDark";
    case RNSBottomTabsTabBarBlurEffect::SystemMaterialDark: return "systemMaterialDark";
    case RNSBottomTabsTabBarBlurEffect::SystemThickMaterialDark: return "systemThickMaterialDark";
    case RNSBottomTabsTabBarBlurEffect::SystemChromeMaterialDark: return "systemChromeMaterialDark";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsTabBarBlurEffect &value) {
  return toString(value);
}
#endif
enum class RNSBottomTabsTabBarItemLabelVisibilityMode { Auto, Selected, Labeled, Unlabeled };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsTabBarItemLabelVisibilityMode &result) {
  auto string = (std::string)value;
  if (string == "auto") { result = RNSBottomTabsTabBarItemLabelVisibilityMode::Auto; return; }
  if (string == "selected") { result = RNSBottomTabsTabBarItemLabelVisibilityMode::Selected; return; }
  if (string == "labeled") { result = RNSBottomTabsTabBarItemLabelVisibilityMode::Labeled; return; }
  if (string == "unlabeled") { result = RNSBottomTabsTabBarItemLabelVisibilityMode::Unlabeled; return; }
  abort();
}

static inline std::string toString(const RNSBottomTabsTabBarItemLabelVisibilityMode &value) {
  switch (value) {
    case RNSBottomTabsTabBarItemLabelVisibilityMode::Auto: return "auto";
    case RNSBottomTabsTabBarItemLabelVisibilityMode::Selected: return "selected";
    case RNSBottomTabsTabBarItemLabelVisibilityMode::Labeled: return "labeled";
    case RNSBottomTabsTabBarItemLabelVisibilityMode::Unlabeled: return "unlabeled";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsTabBarItemLabelVisibilityMode &value) {
  return toString(value);
}
#endif
enum class RNSBottomTabsTabBarMinimizeBehavior { Automatic, Never, OnScrollDown, OnScrollUp };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsTabBarMinimizeBehavior &result) {
  auto string = (std::string)value;
  if (string == "automatic") { result = RNSBottomTabsTabBarMinimizeBehavior::Automatic; return; }
  if (string == "never") { result = RNSBottomTabsTabBarMinimizeBehavior::Never; return; }
  if (string == "onScrollDown") { result = RNSBottomTabsTabBarMinimizeBehavior::OnScrollDown; return; }
  if (string == "onScrollUp") { result = RNSBottomTabsTabBarMinimizeBehavior::OnScrollUp; return; }
  abort();
}

static inline std::string toString(const RNSBottomTabsTabBarMinimizeBehavior &value) {
  switch (value) {
    case RNSBottomTabsTabBarMinimizeBehavior::Automatic: return "automatic";
    case RNSBottomTabsTabBarMinimizeBehavior::Never: return "never";
    case RNSBottomTabsTabBarMinimizeBehavior::OnScrollDown: return "onScrollDown";
    case RNSBottomTabsTabBarMinimizeBehavior::OnScrollUp: return "onScrollUp";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsTabBarMinimizeBehavior &value) {
  return toString(value);
}
#endif
struct RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct {
  Float horizontal{0.0};
  Float vertical{0.0};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["horizontal"] = horizontal;
    result["vertical"] = vertical;
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_horizontal = map.find("horizontal");
  if (tmp_horizontal != map.end()) {
    fromRawValue(context, tmp_horizontal->second, result.horizontal);
  }
  auto tmp_vertical = map.find("vertical");
  if (tmp_vertical != map.end()) {
    fromRawValue(context, tmp_vertical->second, result.vertical);
  }
}

static inline std::string toString(const RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct &value) {
  return "[Object RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct &value) {
  return value.toDynamic();
}
#endif
class RNSBottomTabsProps final : public ViewProps {
 public:
  RNSBottomTabsProps() = default;
  RNSBottomTabsProps(const PropsParserContext& context, const RNSBottomTabsProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  SharedColor tabBarBackgroundColor{};
  RNSBottomTabsTabBarBlurEffect tabBarBlurEffect{RNSBottomTabsTabBarBlurEffect::SystemDefault};
  SharedColor tabBarTintColor{};
  std::string tabBarItemTitleFontFamily{};
  Float tabBarItemTitleFontSize{0.0};
  std::string tabBarItemTitleFontWeight{};
  std::string tabBarItemTitleFontStyle{};
  SharedColor tabBarItemTitleFontColor{};
  RNSBottomTabsTabBarItemTitlePositionAdjustmentStruct tabBarItemTitlePositionAdjustment{};
  SharedColor tabBarItemIconColor{};
  SharedColor tabBarItemBadgeBackgroundColor{};
  SharedColor tabBarItemTitleFontColorActive{};
  SharedColor tabBarItemIconColorActive{};
  Float tabBarItemTitleFontSizeActive{0.0};
  SharedColor tabBarItemActiveIndicatorColor{};
  bool tabBarItemActiveIndicatorEnabled{true};
  SharedColor tabBarItemRippleColor{};
  RNSBottomTabsTabBarItemLabelVisibilityMode tabBarItemLabelVisibilityMode{RNSBottomTabsTabBarItemLabelVisibilityMode::Auto};
  RNSBottomTabsTabBarMinimizeBehavior tabBarMinimizeBehavior{RNSBottomTabsTabBarMinimizeBehavior::Automatic};
  bool controlNavigationStateInJS{false};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSBottomTabsScreenTabBarBlurEffect { None, SystemDefault, ExtraLight, Light, Dark, Regular, Prominent, SystemUltraThinMaterial, SystemThinMaterial, SystemMaterial, SystemThickMaterial, SystemChromeMaterial, SystemUltraThinMaterialLight, SystemThinMaterialLight, SystemMaterialLight, SystemThickMaterialLight, SystemChromeMaterialLight, SystemUltraThinMaterialDark, SystemThinMaterialDark, SystemMaterialDark, SystemThickMaterialDark, SystemChromeMaterialDark };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsScreenTabBarBlurEffect &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNSBottomTabsScreenTabBarBlurEffect::None; return; }
  if (string == "systemDefault") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemDefault; return; }
  if (string == "extraLight") { result = RNSBottomTabsScreenTabBarBlurEffect::ExtraLight; return; }
  if (string == "light") { result = RNSBottomTabsScreenTabBarBlurEffect::Light; return; }
  if (string == "dark") { result = RNSBottomTabsScreenTabBarBlurEffect::Dark; return; }
  if (string == "regular") { result = RNSBottomTabsScreenTabBarBlurEffect::Regular; return; }
  if (string == "prominent") { result = RNSBottomTabsScreenTabBarBlurEffect::Prominent; return; }
  if (string == "systemUltraThinMaterial") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemUltraThinMaterial; return; }
  if (string == "systemThinMaterial") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemThinMaterial; return; }
  if (string == "systemMaterial") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemMaterial; return; }
  if (string == "systemThickMaterial") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemThickMaterial; return; }
  if (string == "systemChromeMaterial") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemChromeMaterial; return; }
  if (string == "systemUltraThinMaterialLight") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemUltraThinMaterialLight; return; }
  if (string == "systemThinMaterialLight") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemThinMaterialLight; return; }
  if (string == "systemMaterialLight") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemMaterialLight; return; }
  if (string == "systemThickMaterialLight") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemThickMaterialLight; return; }
  if (string == "systemChromeMaterialLight") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemChromeMaterialLight; return; }
  if (string == "systemUltraThinMaterialDark") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemUltraThinMaterialDark; return; }
  if (string == "systemThinMaterialDark") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemThinMaterialDark; return; }
  if (string == "systemMaterialDark") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemMaterialDark; return; }
  if (string == "systemThickMaterialDark") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemThickMaterialDark; return; }
  if (string == "systemChromeMaterialDark") { result = RNSBottomTabsScreenTabBarBlurEffect::SystemChromeMaterialDark; return; }
  abort();
}

static inline std::string toString(const RNSBottomTabsScreenTabBarBlurEffect &value) {
  switch (value) {
    case RNSBottomTabsScreenTabBarBlurEffect::None: return "none";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemDefault: return "systemDefault";
    case RNSBottomTabsScreenTabBarBlurEffect::ExtraLight: return "extraLight";
    case RNSBottomTabsScreenTabBarBlurEffect::Light: return "light";
    case RNSBottomTabsScreenTabBarBlurEffect::Dark: return "dark";
    case RNSBottomTabsScreenTabBarBlurEffect::Regular: return "regular";
    case RNSBottomTabsScreenTabBarBlurEffect::Prominent: return "prominent";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemUltraThinMaterial: return "systemUltraThinMaterial";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemThinMaterial: return "systemThinMaterial";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemMaterial: return "systemMaterial";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemThickMaterial: return "systemThickMaterial";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemChromeMaterial: return "systemChromeMaterial";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemUltraThinMaterialLight: return "systemUltraThinMaterialLight";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemThinMaterialLight: return "systemThinMaterialLight";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemMaterialLight: return "systemMaterialLight";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemThickMaterialLight: return "systemThickMaterialLight";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemChromeMaterialLight: return "systemChromeMaterialLight";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemUltraThinMaterialDark: return "systemUltraThinMaterialDark";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemThinMaterialDark: return "systemThinMaterialDark";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemMaterialDark: return "systemMaterialDark";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemThickMaterialDark: return "systemThickMaterialDark";
    case RNSBottomTabsScreenTabBarBlurEffect::SystemChromeMaterialDark: return "systemChromeMaterialDark";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsScreenTabBarBlurEffect &value) {
  return toString(value);
}
#endif
enum class RNSBottomTabsScreenIconType { Image, Template, SfSymbol };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsScreenIconType &result) {
  auto string = (std::string)value;
  if (string == "image") { result = RNSBottomTabsScreenIconType::Image; return; }
  if (string == "template") { result = RNSBottomTabsScreenIconType::Template; return; }
  if (string == "sfSymbol") { result = RNSBottomTabsScreenIconType::SfSymbol; return; }
  abort();
}

static inline std::string toString(const RNSBottomTabsScreenIconType &value) {
  switch (value) {
    case RNSBottomTabsScreenIconType::Image: return "image";
    case RNSBottomTabsScreenIconType::Template: return "template";
    case RNSBottomTabsScreenIconType::SfSymbol: return "sfSymbol";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsScreenIconType &value) {
  return toString(value);
}
#endif
struct RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct {
  Float horizontal{0.0};
  Float vertical{0.0};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["horizontal"] = horizontal;
    result["vertical"] = vertical;
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_horizontal = map.find("horizontal");
  if (tmp_horizontal != map.end()) {
    fromRawValue(context, tmp_horizontal->second, result.horizontal);
  }
  auto tmp_vertical = map.find("vertical");
  if (tmp_vertical != map.end()) {
    fromRawValue(context, tmp_vertical->second, result.vertical);
  }
}

static inline std::string toString(const RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct &value) {
  return "[Object RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct &value) {
  return value.toDynamic();
}
#endif

struct RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct {
  bool popToRoot{true};
  bool scrollToTop{true};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["popToRoot"] = popToRoot;
    result["scrollToTop"] = scrollToTop;
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_popToRoot = map.find("popToRoot");
  if (tmp_popToRoot != map.end()) {
    fromRawValue(context, tmp_popToRoot->second, result.popToRoot);
  }
  auto tmp_scrollToTop = map.find("scrollToTop");
  if (tmp_scrollToTop != map.end()) {
    fromRawValue(context, tmp_scrollToTop->second, result.scrollToTop);
  }
}

static inline std::string toString(const RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct &value) {
  return "[Object RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct &value) {
  return value.toDynamic();
}
#endif

struct RNSBottomTabsScreenSpecialEffectsStruct {
  RNSBottomTabsScreenSpecialEffectsRepeatedTabSelectionStruct repeatedTabSelection{};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSBottomTabsScreenSpecialEffectsStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["repeatedTabSelection"] = ::facebook::react::toDynamic(repeatedTabSelection);
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSBottomTabsScreenSpecialEffectsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_repeatedTabSelection = map.find("repeatedTabSelection");
  if (tmp_repeatedTabSelection != map.end()) {
    fromRawValue(context, tmp_repeatedTabSelection->second, result.repeatedTabSelection);
  }
}

static inline std::string toString(const RNSBottomTabsScreenSpecialEffectsStruct &value) {
  return "[Object RNSBottomTabsScreenSpecialEffectsStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSBottomTabsScreenSpecialEffectsStruct &value) {
  return value.toDynamic();
}
#endif
class RNSBottomTabsScreenProps final : public ViewProps {
 public:
  RNSBottomTabsScreenProps() = default;
  RNSBottomTabsScreenProps(const PropsParserContext& context, const RNSBottomTabsScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool isFocused{false};
  std::string tabKey{};
  SharedColor tabBarBackgroundColor{};
  RNSBottomTabsScreenTabBarBlurEffect tabBarBlurEffect{RNSBottomTabsScreenTabBarBlurEffect::SystemDefault};
  std::string tabBarItemTitleFontFamily{};
  Float tabBarItemTitleFontSize{0.0};
  std::string tabBarItemTitleFontWeight{};
  std::string tabBarItemTitleFontStyle{};
  SharedColor tabBarItemTitleFontColor{};
  RNSBottomTabsScreenTabBarItemTitlePositionAdjustmentStruct tabBarItemTitlePositionAdjustment{};
  SharedColor tabBarItemIconColor{};
  SharedColor tabBarItemBadgeBackgroundColor{};
  std::string title{};
  std::string iconResourceName{};
  SharedColor tabBarItemBadgeTextColor{};
  RNSBottomTabsScreenIconType iconType{RNSBottomTabsScreenIconType::SfSymbol};
  ImageSource iconImageSource{};
  std::string iconSfSymbolName{};
  ImageSource selectedIconImageSource{};
  std::string selectedIconSfSymbolName{};
  std::string badgeValue{};
  RNSBottomTabsScreenSpecialEffectsStruct specialEffects{};
  bool overrideScrollViewContentInsetAdjustmentBehavior{true};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSFullWindowOverlayProps final : public ViewProps {
 public:
  RNSFullWindowOverlayProps() = default;
  RNSFullWindowOverlayProps(const PropsParserContext& context, const RNSFullWindowOverlayProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool accessibilityContainerViewIsModal{true};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSScreenStackHostProps final : public ViewProps {
 public:
  RNSScreenStackHostProps() = default;
  RNSScreenStackHostProps(const PropsParserContext& context, const RNSScreenStackHostProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSSplitViewHostPreferredDisplayMode { Automatic, SecondaryOnly, OneBesideSecondary, OneOverSecondary, TwoBesideSecondary, TwoOverSecondary, TwoDisplaceSecondary };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSplitViewHostPreferredDisplayMode &result) {
  auto string = (std::string)value;
  if (string == "automatic") { result = RNSSplitViewHostPreferredDisplayMode::Automatic; return; }
  if (string == "secondaryOnly") { result = RNSSplitViewHostPreferredDisplayMode::SecondaryOnly; return; }
  if (string == "oneBesideSecondary") { result = RNSSplitViewHostPreferredDisplayMode::OneBesideSecondary; return; }
  if (string == "oneOverSecondary") { result = RNSSplitViewHostPreferredDisplayMode::OneOverSecondary; return; }
  if (string == "twoBesideSecondary") { result = RNSSplitViewHostPreferredDisplayMode::TwoBesideSecondary; return; }
  if (string == "twoOverSecondary") { result = RNSSplitViewHostPreferredDisplayMode::TwoOverSecondary; return; }
  if (string == "twoDisplaceSecondary") { result = RNSSplitViewHostPreferredDisplayMode::TwoDisplaceSecondary; return; }
  abort();
}

static inline std::string toString(const RNSSplitViewHostPreferredDisplayMode &value) {
  switch (value) {
    case RNSSplitViewHostPreferredDisplayMode::Automatic: return "automatic";
    case RNSSplitViewHostPreferredDisplayMode::SecondaryOnly: return "secondaryOnly";
    case RNSSplitViewHostPreferredDisplayMode::OneBesideSecondary: return "oneBesideSecondary";
    case RNSSplitViewHostPreferredDisplayMode::OneOverSecondary: return "oneOverSecondary";
    case RNSSplitViewHostPreferredDisplayMode::TwoBesideSecondary: return "twoBesideSecondary";
    case RNSSplitViewHostPreferredDisplayMode::TwoOverSecondary: return "twoOverSecondary";
    case RNSSplitViewHostPreferredDisplayMode::TwoDisplaceSecondary: return "twoDisplaceSecondary";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSplitViewHostPreferredDisplayMode &value) {
  return toString(value);
}
#endif
enum class RNSSplitViewHostPreferredSplitBehavior { Automatic, Displace, Overlay, Tile };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSplitViewHostPreferredSplitBehavior &result) {
  auto string = (std::string)value;
  if (string == "automatic") { result = RNSSplitViewHostPreferredSplitBehavior::Automatic; return; }
  if (string == "displace") { result = RNSSplitViewHostPreferredSplitBehavior::Displace; return; }
  if (string == "overlay") { result = RNSSplitViewHostPreferredSplitBehavior::Overlay; return; }
  if (string == "tile") { result = RNSSplitViewHostPreferredSplitBehavior::Tile; return; }
  abort();
}

static inline std::string toString(const RNSSplitViewHostPreferredSplitBehavior &value) {
  switch (value) {
    case RNSSplitViewHostPreferredSplitBehavior::Automatic: return "automatic";
    case RNSSplitViewHostPreferredSplitBehavior::Displace: return "displace";
    case RNSSplitViewHostPreferredSplitBehavior::Overlay: return "overlay";
    case RNSSplitViewHostPreferredSplitBehavior::Tile: return "tile";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSplitViewHostPreferredSplitBehavior &value) {
  return toString(value);
}
#endif
enum class RNSSplitViewHostPrimaryEdge { Leading, Trailing };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSplitViewHostPrimaryEdge &result) {
  auto string = (std::string)value;
  if (string == "leading") { result = RNSSplitViewHostPrimaryEdge::Leading; return; }
  if (string == "trailing") { result = RNSSplitViewHostPrimaryEdge::Trailing; return; }
  abort();
}

static inline std::string toString(const RNSSplitViewHostPrimaryEdge &value) {
  switch (value) {
    case RNSSplitViewHostPrimaryEdge::Leading: return "leading";
    case RNSSplitViewHostPrimaryEdge::Trailing: return "trailing";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSplitViewHostPrimaryEdge &value) {
  return toString(value);
}
#endif
enum class RNSSplitViewHostDisplayModeButtonVisibility { Always, Automatic, Never };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSplitViewHostDisplayModeButtonVisibility &result) {
  auto string = (std::string)value;
  if (string == "always") { result = RNSSplitViewHostDisplayModeButtonVisibility::Always; return; }
  if (string == "automatic") { result = RNSSplitViewHostDisplayModeButtonVisibility::Automatic; return; }
  if (string == "never") { result = RNSSplitViewHostDisplayModeButtonVisibility::Never; return; }
  abort();
}

static inline std::string toString(const RNSSplitViewHostDisplayModeButtonVisibility &value) {
  switch (value) {
    case RNSSplitViewHostDisplayModeButtonVisibility::Always: return "always";
    case RNSSplitViewHostDisplayModeButtonVisibility::Automatic: return "automatic";
    case RNSSplitViewHostDisplayModeButtonVisibility::Never: return "never";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSplitViewHostDisplayModeButtonVisibility &value) {
  return toString(value);
}
#endif
struct RNSSplitViewHostColumnMetricsStruct {
  Float minimumPrimaryColumnWidth{-1.0};
  Float maximumPrimaryColumnWidth{-1.0};
  Float preferredPrimaryColumnWidthOrFraction{-1.0};
  Float minimumSupplementaryColumnWidth{-1.0};
  Float maximumSupplementaryColumnWidth{-1.0};
  Float preferredSupplementaryColumnWidthOrFraction{-1.0};
  Float minimumSecondaryColumnWidth{-1.0};
  Float preferredSecondaryColumnWidthOrFraction{-1.0};
  Float minimumInspectorColumnWidth{-1.0};
  Float maximumInspectorColumnWidth{-1.0};
  Float preferredInspectorColumnWidthOrFraction{-1.0};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSSplitViewHostColumnMetricsStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["minimumPrimaryColumnWidth"] = minimumPrimaryColumnWidth;
    result["maximumPrimaryColumnWidth"] = maximumPrimaryColumnWidth;
    result["preferredPrimaryColumnWidthOrFraction"] = preferredPrimaryColumnWidthOrFraction;
    result["minimumSupplementaryColumnWidth"] = minimumSupplementaryColumnWidth;
    result["maximumSupplementaryColumnWidth"] = maximumSupplementaryColumnWidth;
    result["preferredSupplementaryColumnWidthOrFraction"] = preferredSupplementaryColumnWidthOrFraction;
    result["minimumSecondaryColumnWidth"] = minimumSecondaryColumnWidth;
    result["preferredSecondaryColumnWidthOrFraction"] = preferredSecondaryColumnWidthOrFraction;
    result["minimumInspectorColumnWidth"] = minimumInspectorColumnWidth;
    result["maximumInspectorColumnWidth"] = maximumInspectorColumnWidth;
    result["preferredInspectorColumnWidthOrFraction"] = preferredInspectorColumnWidthOrFraction;
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSplitViewHostColumnMetricsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_minimumPrimaryColumnWidth = map.find("minimumPrimaryColumnWidth");
  if (tmp_minimumPrimaryColumnWidth != map.end()) {
    fromRawValue(context, tmp_minimumPrimaryColumnWidth->second, result.minimumPrimaryColumnWidth);
  }
  auto tmp_maximumPrimaryColumnWidth = map.find("maximumPrimaryColumnWidth");
  if (tmp_maximumPrimaryColumnWidth != map.end()) {
    fromRawValue(context, tmp_maximumPrimaryColumnWidth->second, result.maximumPrimaryColumnWidth);
  }
  auto tmp_preferredPrimaryColumnWidthOrFraction = map.find("preferredPrimaryColumnWidthOrFraction");
  if (tmp_preferredPrimaryColumnWidthOrFraction != map.end()) {
    fromRawValue(context, tmp_preferredPrimaryColumnWidthOrFraction->second, result.preferredPrimaryColumnWidthOrFraction);
  }
  auto tmp_minimumSupplementaryColumnWidth = map.find("minimumSupplementaryColumnWidth");
  if (tmp_minimumSupplementaryColumnWidth != map.end()) {
    fromRawValue(context, tmp_minimumSupplementaryColumnWidth->second, result.minimumSupplementaryColumnWidth);
  }
  auto tmp_maximumSupplementaryColumnWidth = map.find("maximumSupplementaryColumnWidth");
  if (tmp_maximumSupplementaryColumnWidth != map.end()) {
    fromRawValue(context, tmp_maximumSupplementaryColumnWidth->second, result.maximumSupplementaryColumnWidth);
  }
  auto tmp_preferredSupplementaryColumnWidthOrFraction = map.find("preferredSupplementaryColumnWidthOrFraction");
  if (tmp_preferredSupplementaryColumnWidthOrFraction != map.end()) {
    fromRawValue(context, tmp_preferredSupplementaryColumnWidthOrFraction->second, result.preferredSupplementaryColumnWidthOrFraction);
  }
  auto tmp_minimumSecondaryColumnWidth = map.find("minimumSecondaryColumnWidth");
  if (tmp_minimumSecondaryColumnWidth != map.end()) {
    fromRawValue(context, tmp_minimumSecondaryColumnWidth->second, result.minimumSecondaryColumnWidth);
  }
  auto tmp_preferredSecondaryColumnWidthOrFraction = map.find("preferredSecondaryColumnWidthOrFraction");
  if (tmp_preferredSecondaryColumnWidthOrFraction != map.end()) {
    fromRawValue(context, tmp_preferredSecondaryColumnWidthOrFraction->second, result.preferredSecondaryColumnWidthOrFraction);
  }
  auto tmp_minimumInspectorColumnWidth = map.find("minimumInspectorColumnWidth");
  if (tmp_minimumInspectorColumnWidth != map.end()) {
    fromRawValue(context, tmp_minimumInspectorColumnWidth->second, result.minimumInspectorColumnWidth);
  }
  auto tmp_maximumInspectorColumnWidth = map.find("maximumInspectorColumnWidth");
  if (tmp_maximumInspectorColumnWidth != map.end()) {
    fromRawValue(context, tmp_maximumInspectorColumnWidth->second, result.maximumInspectorColumnWidth);
  }
  auto tmp_preferredInspectorColumnWidthOrFraction = map.find("preferredInspectorColumnWidthOrFraction");
  if (tmp_preferredInspectorColumnWidthOrFraction != map.end()) {
    fromRawValue(context, tmp_preferredInspectorColumnWidthOrFraction->second, result.preferredInspectorColumnWidthOrFraction);
  }
}

static inline std::string toString(const RNSSplitViewHostColumnMetricsStruct &value) {
  return "[Object RNSSplitViewHostColumnMetricsStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSplitViewHostColumnMetricsStruct &value) {
  return value.toDynamic();
}
#endif
class RNSSplitViewHostProps final : public ViewProps {
 public:
  RNSSplitViewHostProps() = default;
  RNSSplitViewHostProps(const PropsParserContext& context, const RNSSplitViewHostProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNSSplitViewHostPreferredDisplayMode preferredDisplayMode{RNSSplitViewHostPreferredDisplayMode::Automatic};
  RNSSplitViewHostPreferredSplitBehavior preferredSplitBehavior{RNSSplitViewHostPreferredSplitBehavior::Automatic};
  RNSSplitViewHostPrimaryEdge primaryEdge{RNSSplitViewHostPrimaryEdge::Leading};
  bool showSecondaryToggleButton{false};
  RNSSplitViewHostDisplayModeButtonVisibility displayModeButtonVisibility{RNSSplitViewHostDisplayModeButtonVisibility::Automatic};
  RNSSplitViewHostColumnMetricsStruct columnMetrics{};
  bool presentsWithGesture{true};
  bool showInspector{false};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSSplitViewScreenColumnType { Column, Inspector };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSplitViewScreenColumnType &result) {
  auto string = (std::string)value;
  if (string == "column") { result = RNSSplitViewScreenColumnType::Column; return; }
  if (string == "inspector") { result = RNSSplitViewScreenColumnType::Inspector; return; }
  abort();
}

static inline std::string toString(const RNSSplitViewScreenColumnType &value) {
  switch (value) {
    case RNSSplitViewScreenColumnType::Column: return "column";
    case RNSSplitViewScreenColumnType::Inspector: return "inspector";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSplitViewScreenColumnType &value) {
  return toString(value);
}
#endif

class RNSSplitViewScreenProps final : public ViewProps {
 public:
  RNSSplitViewScreenProps() = default;
  RNSSplitViewScreenProps(const PropsParserContext& context, const RNSSplitViewScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNSSplitViewScreenColumnType columnType{RNSSplitViewScreenColumnType::Column};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSStackScreenProps final : public ViewProps {
 public:
  RNSStackScreenProps() = default;
  RNSStackScreenProps(const PropsParserContext& context, const RNSStackScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  int maxLifecycleState{0};
  std::string screenKey{};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSModalScreenStackPresentation { Push, Modal, TransparentModal, FullScreenModal, FormSheet, PageSheet, ContainedModal, ContainedTransparentModal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenStackPresentation &result) {
  auto string = (std::string)value;
  if (string == "push") { result = RNSModalScreenStackPresentation::Push; return; }
  if (string == "modal") { result = RNSModalScreenStackPresentation::Modal; return; }
  if (string == "transparentModal") { result = RNSModalScreenStackPresentation::TransparentModal; return; }
  if (string == "fullScreenModal") { result = RNSModalScreenStackPresentation::FullScreenModal; return; }
  if (string == "formSheet") { result = RNSModalScreenStackPresentation::FormSheet; return; }
  if (string == "pageSheet") { result = RNSModalScreenStackPresentation::PageSheet; return; }
  if (string == "containedModal") { result = RNSModalScreenStackPresentation::ContainedModal; return; }
  if (string == "containedTransparentModal") { result = RNSModalScreenStackPresentation::ContainedTransparentModal; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenStackPresentation &value) {
  switch (value) {
    case RNSModalScreenStackPresentation::Push: return "push";
    case RNSModalScreenStackPresentation::Modal: return "modal";
    case RNSModalScreenStackPresentation::TransparentModal: return "transparentModal";
    case RNSModalScreenStackPresentation::FullScreenModal: return "fullScreenModal";
    case RNSModalScreenStackPresentation::FormSheet: return "formSheet";
    case RNSModalScreenStackPresentation::PageSheet: return "pageSheet";
    case RNSModalScreenStackPresentation::ContainedModal: return "containedModal";
    case RNSModalScreenStackPresentation::ContainedTransparentModal: return "containedTransparentModal";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSModalScreenStackPresentation &value) {
  return toString(value);
}
#endif
enum class RNSModalScreenStackAnimation { Default, Flip, Simple_push, None, Fade, Slide_from_right, Slide_from_left, Slide_from_bottom, Fade_from_bottom, Ios_from_right, Ios_from_left };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenStackAnimation &result) {
  auto string = (std::string)value;
  if (string == "default") { result = RNSModalScreenStackAnimation::Default; return; }
  if (string == "flip") { result = RNSModalScreenStackAnimation::Flip; return; }
  if (string == "simple_push") { result = RNSModalScreenStackAnimation::Simple_push; return; }
  if (string == "none") { result = RNSModalScreenStackAnimation::None; return; }
  if (string == "fade") { result = RNSModalScreenStackAnimation::Fade; return; }
  if (string == "slide_from_right") { result = RNSModalScreenStackAnimation::Slide_from_right; return; }
  if (string == "slide_from_left") { result = RNSModalScreenStackAnimation::Slide_from_left; return; }
  if (string == "slide_from_bottom") { result = RNSModalScreenStackAnimation::Slide_from_bottom; return; }
  if (string == "fade_from_bottom") { result = RNSModalScreenStackAnimation::Fade_from_bottom; return; }
  if (string == "ios_from_right") { result = RNSModalScreenStackAnimation::Ios_from_right; return; }
  if (string == "ios_from_left") { result = RNSModalScreenStackAnimation::Ios_from_left; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenStackAnimation &value) {
  switch (value) {
    case RNSModalScreenStackAnimation::Default: return "default";
    case RNSModalScreenStackAnimation::Flip: return "flip";
    case RNSModalScreenStackAnimation::Simple_push: return "simple_push";
    case RNSModalScreenStackAnimation::None: return "none";
    case RNSModalScreenStackAnimation::Fade: return "fade";
    case RNSModalScreenStackAnimation::Slide_from_right: return "slide_from_right";
    case RNSModalScreenStackAnimation::Slide_from_left: return "slide_from_left";
    case RNSModalScreenStackAnimation::Slide_from_bottom: return "slide_from_bottom";
    case RNSModalScreenStackAnimation::Fade_from_bottom: return "fade_from_bottom";
    case RNSModalScreenStackAnimation::Ios_from_right: return "ios_from_right";
    case RNSModalScreenStackAnimation::Ios_from_left: return "ios_from_left";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSModalScreenStackAnimation &value) {
  return toString(value);
}
#endif
enum class RNSModalScreenReplaceAnimation { Pop, Push };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenReplaceAnimation &result) {
  auto string = (std::string)value;
  if (string == "pop") { result = RNSModalScreenReplaceAnimation::Pop; return; }
  if (string == "push") { result = RNSModalScreenReplaceAnimation::Push; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenReplaceAnimation &value) {
  switch (value) {
    case RNSModalScreenReplaceAnimation::Pop: return "pop";
    case RNSModalScreenReplaceAnimation::Push: return "push";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSModalScreenReplaceAnimation &value) {
  return toString(value);
}
#endif
enum class RNSModalScreenSwipeDirection { Vertical, Horizontal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenSwipeDirection &result) {
  auto string = (std::string)value;
  if (string == "vertical") { result = RNSModalScreenSwipeDirection::Vertical; return; }
  if (string == "horizontal") { result = RNSModalScreenSwipeDirection::Horizontal; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenSwipeDirection &value) {
  switch (value) {
    case RNSModalScreenSwipeDirection::Vertical: return "vertical";
    case RNSModalScreenSwipeDirection::Horizontal: return "horizontal";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSModalScreenSwipeDirection &value) {
  return toString(value);
}
#endif
struct RNSModalScreenGestureResponseDistanceStruct {
  Float start{0.0};
  Float end{0.0};
  Float top{0.0};
  Float bottom{0.0};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSModalScreenGestureResponseDistanceStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["start"] = start;
    result["end"] = end;
    result["top"] = top;
    result["bottom"] = bottom;
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenGestureResponseDistanceStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_start = map.find("start");
  if (tmp_start != map.end()) {
    fromRawValue(context, tmp_start->second, result.start);
  }
  auto tmp_end = map.find("end");
  if (tmp_end != map.end()) {
    fromRawValue(context, tmp_end->second, result.end);
  }
  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
}

static inline std::string toString(const RNSModalScreenGestureResponseDistanceStruct &value) {
  return "[Object RNSModalScreenGestureResponseDistanceStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSModalScreenGestureResponseDistanceStruct &value) {
  return value.toDynamic();
}
#endif
class RNSModalScreenProps final : public ViewProps {
 public:
  RNSModalScreenProps() = default;
  RNSModalScreenProps(const PropsParserContext& context, const RNSModalScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string screenId{""};
  std::vector<Float> sheetAllowedDetents{};
  int sheetLargestUndimmedDetent{-1};
  bool sheetGrabberVisible{false};
  Float sheetCornerRadius{-1.0};
  bool sheetExpandsWhenScrolledToEdge{false};
  int sheetInitialDetent{0};
  int sheetElevation{24};
  bool customAnimationOnSwipe{false};
  bool fullScreenSwipeEnabled{false};
  bool fullScreenSwipeShadowEnabled{true};
  bool homeIndicatorHidden{false};
  bool preventNativeDismiss{false};
  bool gestureEnabled{true};
  SharedColor statusBarColor{};
  bool statusBarHidden{false};
  std::string screenOrientation{};
  std::string statusBarAnimation{};
  std::string statusBarStyle{};
  bool statusBarTranslucent{false};
  RNSModalScreenGestureResponseDistanceStruct gestureResponseDistance{};
  RNSModalScreenStackPresentation stackPresentation{RNSModalScreenStackPresentation::Push};
  RNSModalScreenStackAnimation stackAnimation{RNSModalScreenStackAnimation::Default};
  int transitionDuration{500};
  RNSModalScreenReplaceAnimation replaceAnimation{RNSModalScreenReplaceAnimation::Pop};
  RNSModalScreenSwipeDirection swipeDirection{RNSModalScreenSwipeDirection::Horizontal};
  bool hideKeyboardOnSwipe{false};
  Float activityState{-1.0};
  SharedColor navigationBarColor{};
  bool navigationBarTranslucent{false};
  bool navigationBarHidden{false};
  bool nativeBackButtonDismissalEnabled{false};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSScreenContainerProps final : public ViewProps {
 public:
  RNSScreenContainerProps() = default;
  RNSScreenContainerProps(const PropsParserContext& context, const RNSScreenContainerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSScreenContentWrapperProps final : public ViewProps {
 public:
  RNSScreenContentWrapperProps() = default;
  RNSScreenContentWrapperProps(const PropsParserContext& context, const RNSScreenContentWrapperProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSScreenFooterProps final : public ViewProps {
 public:
  RNSScreenFooterProps() = default;
  RNSScreenFooterProps(const PropsParserContext& context, const RNSScreenFooterProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSScreenStackPresentation { Push, Modal, TransparentModal, FullScreenModal, FormSheet, PageSheet, ContainedModal, ContainedTransparentModal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackPresentation &result) {
  auto string = (std::string)value;
  if (string == "push") { result = RNSScreenStackPresentation::Push; return; }
  if (string == "modal") { result = RNSScreenStackPresentation::Modal; return; }
  if (string == "transparentModal") { result = RNSScreenStackPresentation::TransparentModal; return; }
  if (string == "fullScreenModal") { result = RNSScreenStackPresentation::FullScreenModal; return; }
  if (string == "formSheet") { result = RNSScreenStackPresentation::FormSheet; return; }
  if (string == "pageSheet") { result = RNSScreenStackPresentation::PageSheet; return; }
  if (string == "containedModal") { result = RNSScreenStackPresentation::ContainedModal; return; }
  if (string == "containedTransparentModal") { result = RNSScreenStackPresentation::ContainedTransparentModal; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackPresentation &value) {
  switch (value) {
    case RNSScreenStackPresentation::Push: return "push";
    case RNSScreenStackPresentation::Modal: return "modal";
    case RNSScreenStackPresentation::TransparentModal: return "transparentModal";
    case RNSScreenStackPresentation::FullScreenModal: return "fullScreenModal";
    case RNSScreenStackPresentation::FormSheet: return "formSheet";
    case RNSScreenStackPresentation::PageSheet: return "pageSheet";
    case RNSScreenStackPresentation::ContainedModal: return "containedModal";
    case RNSScreenStackPresentation::ContainedTransparentModal: return "containedTransparentModal";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenStackPresentation &value) {
  return toString(value);
}
#endif
enum class RNSScreenStackAnimation { Default, Flip, Simple_push, None, Fade, Slide_from_right, Slide_from_left, Slide_from_bottom, Fade_from_bottom, Ios_from_right, Ios_from_left };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackAnimation &result) {
  auto string = (std::string)value;
  if (string == "default") { result = RNSScreenStackAnimation::Default; return; }
  if (string == "flip") { result = RNSScreenStackAnimation::Flip; return; }
  if (string == "simple_push") { result = RNSScreenStackAnimation::Simple_push; return; }
  if (string == "none") { result = RNSScreenStackAnimation::None; return; }
  if (string == "fade") { result = RNSScreenStackAnimation::Fade; return; }
  if (string == "slide_from_right") { result = RNSScreenStackAnimation::Slide_from_right; return; }
  if (string == "slide_from_left") { result = RNSScreenStackAnimation::Slide_from_left; return; }
  if (string == "slide_from_bottom") { result = RNSScreenStackAnimation::Slide_from_bottom; return; }
  if (string == "fade_from_bottom") { result = RNSScreenStackAnimation::Fade_from_bottom; return; }
  if (string == "ios_from_right") { result = RNSScreenStackAnimation::Ios_from_right; return; }
  if (string == "ios_from_left") { result = RNSScreenStackAnimation::Ios_from_left; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackAnimation &value) {
  switch (value) {
    case RNSScreenStackAnimation::Default: return "default";
    case RNSScreenStackAnimation::Flip: return "flip";
    case RNSScreenStackAnimation::Simple_push: return "simple_push";
    case RNSScreenStackAnimation::None: return "none";
    case RNSScreenStackAnimation::Fade: return "fade";
    case RNSScreenStackAnimation::Slide_from_right: return "slide_from_right";
    case RNSScreenStackAnimation::Slide_from_left: return "slide_from_left";
    case RNSScreenStackAnimation::Slide_from_bottom: return "slide_from_bottom";
    case RNSScreenStackAnimation::Fade_from_bottom: return "fade_from_bottom";
    case RNSScreenStackAnimation::Ios_from_right: return "ios_from_right";
    case RNSScreenStackAnimation::Ios_from_left: return "ios_from_left";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenStackAnimation &value) {
  return toString(value);
}
#endif
enum class RNSScreenReplaceAnimation { Pop, Push };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenReplaceAnimation &result) {
  auto string = (std::string)value;
  if (string == "pop") { result = RNSScreenReplaceAnimation::Pop; return; }
  if (string == "push") { result = RNSScreenReplaceAnimation::Push; return; }
  abort();
}

static inline std::string toString(const RNSScreenReplaceAnimation &value) {
  switch (value) {
    case RNSScreenReplaceAnimation::Pop: return "pop";
    case RNSScreenReplaceAnimation::Push: return "push";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenReplaceAnimation &value) {
  return toString(value);
}
#endif
enum class RNSScreenSwipeDirection { Vertical, Horizontal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenSwipeDirection &result) {
  auto string = (std::string)value;
  if (string == "vertical") { result = RNSScreenSwipeDirection::Vertical; return; }
  if (string == "horizontal") { result = RNSScreenSwipeDirection::Horizontal; return; }
  abort();
}

static inline std::string toString(const RNSScreenSwipeDirection &value) {
  switch (value) {
    case RNSScreenSwipeDirection::Vertical: return "vertical";
    case RNSScreenSwipeDirection::Horizontal: return "horizontal";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenSwipeDirection &value) {
  return toString(value);
}
#endif
struct RNSScreenGestureResponseDistanceStruct {
  Float start{0.0};
  Float end{0.0};
  Float top{0.0};
  Float bottom{0.0};

#ifdef RN_SERIALIZABLE_STATE
  bool operator==(const RNSScreenGestureResponseDistanceStruct&) const = default;

  folly::dynamic toDynamic() const {
    folly::dynamic result = folly::dynamic::object();
    result["start"] = start;
    result["end"] = end;
    result["top"] = top;
    result["bottom"] = bottom;
    return result;
  }
#endif
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenGestureResponseDistanceStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_start = map.find("start");
  if (tmp_start != map.end()) {
    fromRawValue(context, tmp_start->second, result.start);
  }
  auto tmp_end = map.find("end");
  if (tmp_end != map.end()) {
    fromRawValue(context, tmp_end->second, result.end);
  }
  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
}

static inline std::string toString(const RNSScreenGestureResponseDistanceStruct &value) {
  return "[Object RNSScreenGestureResponseDistanceStruct]";
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenGestureResponseDistanceStruct &value) {
  return value.toDynamic();
}
#endif
class RNSScreenProps final : public ViewProps {
 public:
  RNSScreenProps() = default;
  RNSScreenProps(const PropsParserContext& context, const RNSScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string screenId{""};
  std::vector<Float> sheetAllowedDetents{};
  int sheetLargestUndimmedDetent{-1};
  bool sheetGrabberVisible{false};
  Float sheetCornerRadius{-1.0};
  bool sheetExpandsWhenScrolledToEdge{false};
  int sheetInitialDetent{0};
  int sheetElevation{24};
  bool customAnimationOnSwipe{false};
  bool fullScreenSwipeEnabled{false};
  bool fullScreenSwipeShadowEnabled{true};
  bool homeIndicatorHidden{false};
  bool preventNativeDismiss{false};
  bool gestureEnabled{true};
  SharedColor statusBarColor{};
  bool statusBarHidden{false};
  std::string screenOrientation{};
  std::string statusBarAnimation{};
  std::string statusBarStyle{};
  bool statusBarTranslucent{false};
  RNSScreenGestureResponseDistanceStruct gestureResponseDistance{};
  RNSScreenStackPresentation stackPresentation{RNSScreenStackPresentation::Push};
  RNSScreenStackAnimation stackAnimation{RNSScreenStackAnimation::Default};
  int transitionDuration{500};
  RNSScreenReplaceAnimation replaceAnimation{RNSScreenReplaceAnimation::Pop};
  RNSScreenSwipeDirection swipeDirection{RNSScreenSwipeDirection::Horizontal};
  bool hideKeyboardOnSwipe{false};
  Float activityState{-1.0};
  SharedColor navigationBarColor{};
  bool navigationBarTranslucent{false};
  bool navigationBarHidden{false};
  bool nativeBackButtonDismissalEnabled{false};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSScreenNavigationContainerProps final : public ViewProps {
 public:
  RNSScreenNavigationContainerProps() = default;
  RNSScreenNavigationContainerProps(const PropsParserContext& context, const RNSScreenNavigationContainerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSScreenStackHeaderConfigDirection { Rtl, Ltr };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderConfigDirection &result) {
  auto string = (std::string)value;
  if (string == "rtl") { result = RNSScreenStackHeaderConfigDirection::Rtl; return; }
  if (string == "ltr") { result = RNSScreenStackHeaderConfigDirection::Ltr; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderConfigDirection &value) {
  switch (value) {
    case RNSScreenStackHeaderConfigDirection::Rtl: return "rtl";
    case RNSScreenStackHeaderConfigDirection::Ltr: return "ltr";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenStackHeaderConfigDirection &value) {
  return toString(value);
}
#endif
enum class RNSScreenStackHeaderConfigBackButtonDisplayMode { Minimal, Default, Generic };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderConfigBackButtonDisplayMode &result) {
  auto string = (std::string)value;
  if (string == "minimal") { result = RNSScreenStackHeaderConfigBackButtonDisplayMode::Minimal; return; }
  if (string == "default") { result = RNSScreenStackHeaderConfigBackButtonDisplayMode::Default; return; }
  if (string == "generic") { result = RNSScreenStackHeaderConfigBackButtonDisplayMode::Generic; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderConfigBackButtonDisplayMode &value) {
  switch (value) {
    case RNSScreenStackHeaderConfigBackButtonDisplayMode::Minimal: return "minimal";
    case RNSScreenStackHeaderConfigBackButtonDisplayMode::Default: return "default";
    case RNSScreenStackHeaderConfigBackButtonDisplayMode::Generic: return "generic";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenStackHeaderConfigBackButtonDisplayMode &value) {
  return toString(value);
}
#endif
enum class RNSScreenStackHeaderConfigBlurEffect { None, ExtraLight, Light, Dark, Regular, Prominent, SystemUltraThinMaterial, SystemThinMaterial, SystemMaterial, SystemThickMaterial, SystemChromeMaterial, SystemUltraThinMaterialLight, SystemThinMaterialLight, SystemMaterialLight, SystemThickMaterialLight, SystemChromeMaterialLight, SystemUltraThinMaterialDark, SystemThinMaterialDark, SystemMaterialDark, SystemThickMaterialDark, SystemChromeMaterialDark };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderConfigBlurEffect &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNSScreenStackHeaderConfigBlurEffect::None; return; }
  if (string == "extraLight") { result = RNSScreenStackHeaderConfigBlurEffect::ExtraLight; return; }
  if (string == "light") { result = RNSScreenStackHeaderConfigBlurEffect::Light; return; }
  if (string == "dark") { result = RNSScreenStackHeaderConfigBlurEffect::Dark; return; }
  if (string == "regular") { result = RNSScreenStackHeaderConfigBlurEffect::Regular; return; }
  if (string == "prominent") { result = RNSScreenStackHeaderConfigBlurEffect::Prominent; return; }
  if (string == "systemUltraThinMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterial; return; }
  if (string == "systemThinMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterial; return; }
  if (string == "systemMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemMaterial; return; }
  if (string == "systemThickMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterial; return; }
  if (string == "systemChromeMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterial; return; }
  if (string == "systemUltraThinMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialLight; return; }
  if (string == "systemThinMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialLight; return; }
  if (string == "systemMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemMaterialLight; return; }
  if (string == "systemThickMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialLight; return; }
  if (string == "systemChromeMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialLight; return; }
  if (string == "systemUltraThinMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialDark; return; }
  if (string == "systemThinMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialDark; return; }
  if (string == "systemMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemMaterialDark; return; }
  if (string == "systemThickMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialDark; return; }
  if (string == "systemChromeMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialDark; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderConfigBlurEffect &value) {
  switch (value) {
    case RNSScreenStackHeaderConfigBlurEffect::None: return "none";
    case RNSScreenStackHeaderConfigBlurEffect::ExtraLight: return "extraLight";
    case RNSScreenStackHeaderConfigBlurEffect::Light: return "light";
    case RNSScreenStackHeaderConfigBlurEffect::Dark: return "dark";
    case RNSScreenStackHeaderConfigBlurEffect::Regular: return "regular";
    case RNSScreenStackHeaderConfigBlurEffect::Prominent: return "prominent";
    case RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterial: return "systemUltraThinMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterial: return "systemThinMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemMaterial: return "systemMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterial: return "systemThickMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterial: return "systemChromeMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialLight: return "systemUltraThinMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialLight: return "systemThinMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemMaterialLight: return "systemMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialLight: return "systemThickMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialLight: return "systemChromeMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialDark: return "systemUltraThinMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialDark: return "systemThinMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemMaterialDark: return "systemMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialDark: return "systemThickMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialDark: return "systemChromeMaterialDark";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenStackHeaderConfigBlurEffect &value) {
  return toString(value);
}
#endif

class RNSScreenStackHeaderConfigProps final : public ViewProps {
 public:
  RNSScreenStackHeaderConfigProps() = default;
  RNSScreenStackHeaderConfigProps(const PropsParserContext& context, const RNSScreenStackHeaderConfigProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  SharedColor backgroundColor{};
  std::string backTitle{};
  std::string backTitleFontFamily{};
  int backTitleFontSize{0};
  bool backTitleVisible{true};
  SharedColor color{};
  RNSScreenStackHeaderConfigDirection direction{RNSScreenStackHeaderConfigDirection::Ltr};
  bool hidden{false};
  bool hideShadow{false};
  bool largeTitle{false};
  std::string largeTitleFontFamily{};
  int largeTitleFontSize{0};
  std::string largeTitleFontWeight{};
  SharedColor largeTitleBackgroundColor{};
  bool largeTitleHideShadow{false};
  SharedColor largeTitleColor{};
  bool translucent{false};
  std::string title{};
  std::string titleFontFamily{};
  int titleFontSize{0};
  std::string titleFontWeight{};
  SharedColor titleColor{};
  bool disableBackButtonMenu{false};
  RNSScreenStackHeaderConfigBackButtonDisplayMode backButtonDisplayMode{RNSScreenStackHeaderConfigBackButtonDisplayMode::Default};
  bool hideBackButton{false};
  bool backButtonInCustomView{false};
  RNSScreenStackHeaderConfigBlurEffect blurEffect{RNSScreenStackHeaderConfigBlurEffect::None};
  bool topInsetEnabled{false};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSScreenStackHeaderSubviewType { Back, Right, Left, Title, Center, SearchBar };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderSubviewType &result) {
  auto string = (std::string)value;
  if (string == "back") { result = RNSScreenStackHeaderSubviewType::Back; return; }
  if (string == "right") { result = RNSScreenStackHeaderSubviewType::Right; return; }
  if (string == "left") { result = RNSScreenStackHeaderSubviewType::Left; return; }
  if (string == "title") { result = RNSScreenStackHeaderSubviewType::Title; return; }
  if (string == "center") { result = RNSScreenStackHeaderSubviewType::Center; return; }
  if (string == "searchBar") { result = RNSScreenStackHeaderSubviewType::SearchBar; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderSubviewType &value) {
  switch (value) {
    case RNSScreenStackHeaderSubviewType::Back: return "back";
    case RNSScreenStackHeaderSubviewType::Right: return "right";
    case RNSScreenStackHeaderSubviewType::Left: return "left";
    case RNSScreenStackHeaderSubviewType::Title: return "title";
    case RNSScreenStackHeaderSubviewType::Center: return "center";
    case RNSScreenStackHeaderSubviewType::SearchBar: return "searchBar";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSScreenStackHeaderSubviewType &value) {
  return toString(value);
}
#endif

class RNSScreenStackHeaderSubviewProps final : public ViewProps {
 public:
  RNSScreenStackHeaderSubviewProps() = default;
  RNSScreenStackHeaderSubviewProps(const PropsParserContext& context, const RNSScreenStackHeaderSubviewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNSScreenStackHeaderSubviewType type{RNSScreenStackHeaderSubviewType::Left};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

class RNSScreenStackProps final : public ViewProps {
 public:
  RNSScreenStackProps() = default;
  RNSScreenStackProps(const PropsParserContext& context, const RNSScreenStackProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

enum class RNSSearchBarAutoCapitalize { None, Words, Sentences, Characters };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSearchBarAutoCapitalize &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNSSearchBarAutoCapitalize::None; return; }
  if (string == "words") { result = RNSSearchBarAutoCapitalize::Words; return; }
  if (string == "sentences") { result = RNSSearchBarAutoCapitalize::Sentences; return; }
  if (string == "characters") { result = RNSSearchBarAutoCapitalize::Characters; return; }
  abort();
}

static inline std::string toString(const RNSSearchBarAutoCapitalize &value) {
  switch (value) {
    case RNSSearchBarAutoCapitalize::None: return "none";
    case RNSSearchBarAutoCapitalize::Words: return "words";
    case RNSSearchBarAutoCapitalize::Sentences: return "sentences";
    case RNSSearchBarAutoCapitalize::Characters: return "characters";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSearchBarAutoCapitalize &value) {
  return toString(value);
}
#endif
enum class RNSSearchBarPlacement { Automatic, Inline, Stacked };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSearchBarPlacement &result) {
  auto string = (std::string)value;
  if (string == "automatic") { result = RNSSearchBarPlacement::Automatic; return; }
  if (string == "inline") { result = RNSSearchBarPlacement::Inline; return; }
  if (string == "stacked") { result = RNSSearchBarPlacement::Stacked; return; }
  abort();
}

static inline std::string toString(const RNSSearchBarPlacement &value) {
  switch (value) {
    case RNSSearchBarPlacement::Automatic: return "automatic";
    case RNSSearchBarPlacement::Inline: return "inline";
    case RNSSearchBarPlacement::Stacked: return "stacked";
  }
}

#ifdef RN_SERIALIZABLE_STATE
static inline folly::dynamic toDynamic(const RNSSearchBarPlacement &value) {
  return toString(value);
}
#endif

class RNSSearchBarProps final : public ViewProps {
 public:
  RNSSearchBarProps() = default;
  RNSSearchBarProps(const PropsParserContext& context, const RNSSearchBarProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool hideWhenScrolling{false};
  RNSSearchBarAutoCapitalize autoCapitalize{RNSSearchBarAutoCapitalize::None};
  std::string placeholder{};
  RNSSearchBarPlacement placement{RNSSearchBarPlacement::Stacked};
  bool obscureBackground{false};
  bool hideNavigationBar{false};
  std::string cancelButtonText{};
  SharedColor barTintColor{};
  SharedColor tintColor{};
  SharedColor textColor{};
  bool disableBackButtonOverride{false};
  std::string inputType{};
  SharedColor hintTextColor{};
  SharedColor headerIconColor{};
  bool shouldShowHintSearchIcon{true};

  #ifdef RN_SERIALIZABLE_STATE
  ComponentName getDiffPropsImplementationTarget() const override;

  folly::dynamic getDiffProps(const Props* prevProps) const override;
  #endif
};

} // namespace facebook::react
