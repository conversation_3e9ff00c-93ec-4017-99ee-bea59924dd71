# BeautyHub Backend Setup Guide

This guide covers the complete backend setup for BeautyHub using Supabase as the primary backend service.

## 🏗️ Architecture Overview

BeautyHub uses a **Supabase-first architecture** with the following components:

- **Database**: PostgreSQL with PostGIS for location data
- **Authentication**: Supabase Auth with JWT tokens
- **Storage**: Supabase Storage for images and files
- **Edge Functions**: Serverless functions for business logic
- **Real-time**: WebSocket subscriptions for live updates

## 📋 Prerequisites

Before starting, ensure you have:

- [x] Node.js 18+ installed
- [x] Docker Desktop (for local Supabase)
- [x] Supabase CLI installed
- [x] PostgreSQL knowledge (basic)
- [x] TypeScript familiarity

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install Supabase CLI globally
npm install -g supabase

# Install project dependencies
npm install
```

### 2. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# For local development, you can use the default values
```

### 3. Start Local Supabase

```bash
# Start all Supabase services
supabase start

# This will start:
# - PostgreSQL database
# - API server
# - Auth server
# - Storage server
# - Edge Functions runtime
# - Supabase Studio (dashboard)
```

### 4. Run Setup Script

```bash
# Run the automated setup script
./scripts/setup-dev.sh
```

## 📊 Database Schema

The database includes the following main tables:

### Core Tables

- **users** - User profiles (extends Supabase auth.users)
- **beauty_technicians** - Technician profiles and business info
- **services** - Services offered by technicians
- **bookings** - Appointment bookings
- **reviews** - Customer reviews and ratings
- **notifications** - In-app notifications
- **account_settings** - User preferences
- **payment_accounts** - Stripe account information
- **audit_logs** - System activity logs

### Key Features

- **Row Level Security (RLS)** - Data access control
- **PostGIS Integration** - Location-based queries
- **Automated Triggers** - Updated timestamps
- **Database Functions** - Business logic in SQL
- **Indexes** - Optimized query performance

## 🔐 Authentication

### Supabase Auth Integration

```typescript
import { authHelpers } from './src/services/supabase'

// Sign up
const { data, error } = await authHelpers.signUp(email, password, {
  name: 'John Doe',
  role: 'customer'
})

// Sign in
const { data, error } = await authHelpers.signIn(email, password)

// Get current user
const { user, error } = await authHelpers.getCurrentUser()
```

### Role-Based Access

- **Customer**: Book services, leave reviews
- **Technician**: Manage services, accept bookings
- **Admin**: System administration

## 💾 Storage

### Bucket Configuration

- **profile-images** (public) - User profile pictures
- **service-images** (public) - Service photos
- **header-images** (public) - Technician header images
- **certifications** (private) - Certification documents
- **review-media** (public) - Review photos/videos

### Usage Example

```typescript
import { storageHelpers } from './src/services/supabase'

// Upload file
const { data, error } = await storageHelpers.uploadFile(
  'profile-images',
  `${userId}/avatar.jpg`,
  file
)

// Get public URL
const url = storageHelpers.getPublicUrl('profile-images', path)
```

## ⚡ Edge Functions

### Available Functions

1. **stripe-webhook** - Handle Stripe payment events
2. **booking-reminders** - Send appointment reminders
3. **auto-release-deposits** - Release payments to technicians

### Deployment

```bash
# Deploy all functions
supabase functions deploy

# Deploy specific function
supabase functions deploy stripe-webhook
```

### Environment Variables

Set these in your Supabase project settings:

```
STRIPE_SECRET_KEY=sk_test_...
SENDGRID_API_KEY=SG....
FCM_SERVER_KEY=AAAA...
```

## 🔄 Real-time Features

### Subscription Examples

```typescript
import { realtimeHelpers } from './src/services/supabase'

// Subscribe to booking updates
const subscription = realtimeHelpers.subscribeToBookings(
  userId,
  'customer',
  (payload) => {
    console.log('Booking updated:', payload)
  }
)

// Unsubscribe
subscription.unsubscribe()
```

## 🧪 Testing

### Local Testing

```bash
# Start local Supabase
supabase start

# Run tests
npm test

# Test specific service
npm run test:auth
```

### Database Testing

```bash
# Reset database with seed data
supabase db reset

# Run migrations only
supabase db push
```

## 📈 Monitoring

### Supabase Studio

Access the local dashboard at: http://127.0.0.1:54323

Features:
- Database browser
- SQL editor
- API documentation
- Storage browser
- Auth management

### Logging

```typescript
// Enable debug logging in development
if (__DEV__) {
  console.log('API call:', { method, url, data })
}
```

## 🚀 Production Deployment

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Note your project URL and anon key

### 2. Deploy Schema

```bash
# Link to remote project
supabase link --project-ref your-project-ref

# Push schema to production
supabase db push
```

### 3. Deploy Functions

```bash
# Deploy edge functions
supabase functions deploy
```

### 4. Configure Environment

Update your production environment variables:

```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

## 🔧 Troubleshooting

### Common Issues

1. **Docker not running**
   ```bash
   # Start Docker Desktop
   open -a Docker
   ```

2. **Port conflicts**
   ```bash
   # Check what's using port 54321
   lsof -i :54321
   ```

3. **Migration errors**
   ```bash
   # Reset and retry
   supabase db reset
   ```

### Getting Help

- [Supabase Documentation](https://supabase.com/docs)
- [Discord Community](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

## 📚 Additional Resources

- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [PostGIS Documentation](https://postgis.net/documentation/)
- [Stripe API Reference](https://stripe.com/docs/api)

---

## 🎯 Next Steps

After completing the backend setup:

1. ✅ Configure external integrations (Stripe, Google Maps, etc.)
2. ✅ Set up push notifications
3. ✅ Deploy to production
4. ✅ Set up monitoring and analytics
5. ✅ Configure CI/CD pipeline

For questions or issues, please refer to the troubleshooting section or create an issue in the project repository.
