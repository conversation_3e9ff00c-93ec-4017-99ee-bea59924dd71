{"name": "BeautyHub", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "ios:device": "react-native run-ios --device", "ios:simulator": "react-native run-ios --simulator=\"iPhone 15\"", "lint": "eslint .", "lint:fix": "eslint . --fix", "start": "react-native start", "start:reset": "react-native start --reset-cache", "test": "jest", "test:watch": "jest --watch", "clean": "cd ios && xcodebuild clean && cd .. && cd android && ./gradlew clean && cd ..", "clean:ios": "cd ios && xcodebuild clean && cd ..", "clean:android": "cd android && ./gradlew clean && cd ..", "pod:install": "cd ios && pod install && cd .."}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.81.0", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "@supabase/supabase-js": "^2.55.0", "axios": "^1.11.0", "react": "19.1.0", "react-native": "0.81.0", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.14.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.0", "@react-native/eslint-config": "0.81.0", "@react-native/metro-config": "0.81.0", "@react-native/typescript-config": "0.81.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}