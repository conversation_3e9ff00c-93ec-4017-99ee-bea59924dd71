-- BeautyHub Seed Data
-- Sample data for development and testing

-- Insert sample users (these would normally be created through Supabase Auth)
-- Note: In production, users are created via auth.users table through Supabase Auth

-- Sample admin user
INSERT INTO users (user_id, role, name, email, phone_number) VALUES
('********-0000-0000-0000-********0001', 'admin', 'Admin User', '<EMAIL>', '+44 7700 900001');

-- Sample customers
INSERT INTO users (user_id, role, name, email, phone_number) VALUES
('********-0000-0000-0000-********0002', 'customer', '<PERSON>', '<EMAIL>', '+44 7700 900002'),
('********-0000-0000-0000-********0003', 'customer', '<PERSON>', '<EMAIL>', '+44 7700 900003'),
('********-0000-0000-0000-********0004', 'customer', '<PERSON>', '<EMAIL>', '+44 7700 900004');

-- <PERSON><PERSON> technicians
INSERT INTO users (user_id, role, name, email, phone_number) VALUES
('********-0000-0000-0000-********0005', 'tech', 'Maya Patel', '<EMAIL>', '+44 7700 900005'),
('********-0000-0000-0000-************', 'tech', 'Zara Ahmed', '<EMAIL>', '+44 7700 900006'),
('********-0000-0000-0000-********0007', 'tech', 'Chloe Davis', '<EMAIL>', '+44 7700 900007');

-- Sample beauty technician profiles
INSERT INTO beauty_technicians (tech_id, studio_name, location, bio, working_hours, verification_status) VALUES
(
    '********-0000-0000-0000-********0005',
    'Maya''s Beauty Studio',
    ST_GeogFromText('POINT(-0.1276 51.5074)'), -- London coordinates
    'Professional nail artist with 5+ years experience. Specializing in gel manicures and nail art.',
    '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}, "saturday": {"start": "10:00", "end": "16:00"}}',
    'verified'
),
(
    '********-0000-0000-0000-************',
    'Zara''s Lash Lounge',
    ST_GeogFromText('POINT(-0.1406 51.5014)'), -- London coordinates
    'Certified lash technician offering classic and volume lash extensions. Mobile service available.',
    '{"tuesday": {"start": "10:00", "end": "18:00"}, "wednesday": {"start": "10:00", "end": "18:00"}, "thursday": {"start": "10:00", "end": "18:00"}, "friday": {"start": "10:00", "end": "18:00"}, "saturday": {"start": "09:00", "end": "17:00"}, "sunday": {"start": "11:00", "end": "15:00"}}',
    'certified'
),
(
    '********-0000-0000-0000-********0007',
    'Chloe''s Brow Bar',
    ST_GeogFromText('POINT(-0.1200 51.5100)'), -- London coordinates
    'Eyebrow specialist with expertise in microblading, threading, and brow lamination.',
    '{"monday": {"start": "10:00", "end": "19:00"}, "tuesday": {"start": "10:00", "end": "19:00"}, "wednesday": {"start": "10:00", "end": "19:00"}, "thursday": {"start": "10:00", "end": "19:00"}, "friday": {"start": "10:00", "end": "19:00"}, "saturday": {"start": "09:00", "end": "18:00"}}',
    'verified'
);

-- Sample services
INSERT INTO services (tech_id, name, description, category, duration_minutes, price_pence, deposit_pence) VALUES
-- Maya's services
('********-0000-0000-0000-********0005', 'Classic Manicure', 'Professional manicure with nail shaping, cuticle care, and polish application.', 'Nails', 45, 2500, 1000),
('********-0000-0000-0000-********0005', 'Gel Manicure', 'Long-lasting gel manicure with UV curing for chip-resistant finish.', 'Nails', 60, 3500, 1500),
('********-0000-0000-0000-********0005', 'Nail Art Design', 'Custom nail art with intricate designs and decorations.', 'Nails', 90, 4500, 2000),

-- Zara's services
('********-0000-0000-0000-************', 'Classic Lash Extensions', 'Natural-looking individual lash extensions for everyday wear.', 'Lashes', 120, 6000, 2500),
('********-0000-0000-0000-************', 'Volume Lash Extensions', 'Dramatic volume lashes using multiple thin extensions per natural lash.', 'Lashes', 150, 8500, 3500),
('********-0000-0000-0000-************', 'Lash Lift & Tint', 'Natural lash enhancement with lifting and tinting treatment.', 'Lashes', 75, 4500, 2000),

-- Chloe's services
('********-0000-0000-0000-********0007', 'Eyebrow Threading', 'Precise eyebrow shaping using traditional threading technique.', 'Brows', 30, 1800, 500),
('********-0000-0000-0000-********0007', 'Brow Lamination', 'Brow styling treatment for fuller, more defined eyebrows.', 'Brows', 60, 4000, 1500),
('********-0000-0000-0000-********0007', 'Microblading', 'Semi-permanent eyebrow tattooing for natural-looking fuller brows.', 'Brows', 180, 25000, 10000);

-- Sample account settings
INSERT INTO account_settings (user_id, communication_preferences, privacy_settings) VALUES
('********-0000-0000-0000-********0002', '{"email_notifications": true, "sms_notifications": true, "marketing_emails": false}', '{"profile_visibility": "public", "location_sharing": true}'),
('********-0000-0000-0000-********0003', '{"email_notifications": true, "sms_notifications": false, "marketing_emails": true}', '{"profile_visibility": "public", "location_sharing": true}'),
('********-0000-0000-0000-********0004', '{"email_notifications": false, "sms_notifications": true, "marketing_emails": false}', '{"profile_visibility": "private", "location_sharing": false}'),
('********-0000-0000-0000-********0005', '{"email_notifications": true, "sms_notifications": true, "marketing_emails": true}', '{"profile_visibility": "public", "location_sharing": true}'),
('********-0000-0000-0000-************', '{"email_notifications": true, "sms_notifications": true, "marketing_emails": true}', '{"profile_visibility": "public", "location_sharing": true}'),
('********-0000-0000-0000-********0007', '{"email_notifications": true, "sms_notifications": false, "marketing_emails": true}', '{"profile_visibility": "public", "location_sharing": true}');

-- Sample payment accounts for technicians
INSERT INTO payment_accounts (tech_id, payout_method) VALUES
('********-0000-0000-0000-********0005', 'weekly'),
('********-0000-0000-0000-************', 'instant'),
('********-0000-0000-0000-********0007', 'monthly');

-- Sample bookings (some in the past for review purposes)
INSERT INTO bookings (customer_id, service_id, tech_id, status, appointment_time, deposit_paid, total_amount_pence, payment_status) VALUES
(
    '********-0000-0000-0000-********0002',
    (SELECT service_id FROM services WHERE name = 'Gel Manicure' LIMIT 1),
    '********-0000-0000-0000-********0005',
    'completed',
    NOW() - INTERVAL '1 week',
    true,
    3500,
    'paid'
),
(
    '********-0000-0000-0000-********0003',
    (SELECT service_id FROM services WHERE name = 'Classic Lash Extensions' LIMIT 1),
    '********-0000-0000-0000-************',
    'completed',
    NOW() - INTERVAL '3 days',
    true,
    6000,
    'paid'
);

-- Sample reviews
INSERT INTO reviews (booking_id, reviewer_id, reviewee_id, rating, comment) VALUES
(
    (SELECT booking_id FROM bookings WHERE customer_id = '********-0000-0000-0000-********0002' LIMIT 1),
    '********-0000-0000-0000-********0002',
    '********-0000-0000-0000-********0005',
    5,
    'Amazing service! Maya was so professional and my nails look perfect. Will definitely book again.'
),
(
    (SELECT booking_id FROM bookings WHERE customer_id = '********-0000-0000-0000-********0003' LIMIT 1),
    '********-0000-0000-0000-********0003',
    '********-0000-0000-0000-************',
    5,
    'Zara did an incredible job with my lash extensions. They look so natural and beautiful!'
);
