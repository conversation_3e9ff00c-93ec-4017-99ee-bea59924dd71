import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Verify Stripe webhook signature
    const signature = req.headers.get('stripe-signature')
    const body = await req.text()
    
    if (!signature) {
      throw new Error('No Stripe signature found')
    }

    // Parse the webhook event
    const event = JSON.parse(body)
    
    console.log('Received Stripe webhook:', event.type)

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(supabaseClient, event.data.object)
        break
        
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(supabaseClient, event.data.object)
        break
        
      case 'account.updated':
        await handleAccountUpdated(supabaseClient, event.data.object)
        break
        
      case 'transfer.created':
        await handleTransferCreated(supabaseClient, event.data.object)
        break
        
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(
      JSON.stringify({ received: true }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function handlePaymentSucceeded(supabase: any, paymentIntent: any) {
  const bookingId = paymentIntent.metadata?.booking_id
  
  if (!bookingId) {
    console.error('No booking_id in payment intent metadata')
    return
  }

  // Update booking payment status
  const { error } = await supabase
    .from('bookings')
    .update({
      payment_status: 'paid',
      deposit_paid: true,
      updated_at: new Date().toISOString(),
    })
    .eq('booking_id', bookingId)

  if (error) {
    console.error('Error updating booking payment status:', error)
    throw error
  }

  // Create notification for customer
  const { data: booking } = await supabase
    .from('bookings')
    .select('customer_id, tech_id, appointment_time')
    .eq('booking_id', bookingId)
    .single()

  if (booking) {
    await supabase
      .from('notifications')
      .insert({
        user_id: booking.customer_id,
        type: 'booking',
        message: 'Payment confirmed! Your booking is now secured.',
      })

    // Notify technician
    await supabase
      .from('notifications')
      .insert({
        user_id: booking.tech_id,
        type: 'booking',
        message: 'New booking confirmed with payment received.',
      })
  }

  console.log(`Payment succeeded for booking ${bookingId}`)
}

async function handlePaymentFailed(supabase: any, paymentIntent: any) {
  const bookingId = paymentIntent.metadata?.booking_id
  
  if (!bookingId) {
    console.error('No booking_id in payment intent metadata')
    return
  }

  // Update booking payment status
  const { error } = await supabase
    .from('bookings')
    .update({
      payment_status: 'pending',
      updated_at: new Date().toISOString(),
    })
    .eq('booking_id', bookingId)

  if (error) {
    console.error('Error updating booking payment status:', error)
    throw error
  }

  // Create notification for customer
  const { data: booking } = await supabase
    .from('bookings')
    .select('customer_id')
    .eq('booking_id', bookingId)
    .single()

  if (booking) {
    await supabase
      .from('notifications')
      .insert({
        user_id: booking.customer_id,
        type: 'booking',
        message: 'Payment failed. Please try again to secure your booking.',
      })
  }

  console.log(`Payment failed for booking ${bookingId}`)
}

async function handleAccountUpdated(supabase: any, account: any) {
  // Update technician's Stripe account status
  const { error } = await supabase
    .from('payment_accounts')
    .update({
      stripe_account_id: account.id,
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_account_id', account.id)

  if (error) {
    console.error('Error updating payment account:', error)
  }

  console.log(`Account updated: ${account.id}`)
}

async function handleTransferCreated(supabase: any, transfer: any) {
  // Log transfer for audit purposes
  const { error } = await supabase
    .from('audit_logs')
    .insert({
      action_type: 'stripe_transfer_created',
      action_details: {
        transfer_id: transfer.id,
        amount: transfer.amount,
        destination: transfer.destination,
        created: transfer.created,
      },
    })

  if (error) {
    console.error('Error logging transfer:', error)
  }

  console.log(`Transfer created: ${transfer.id}`)
}
