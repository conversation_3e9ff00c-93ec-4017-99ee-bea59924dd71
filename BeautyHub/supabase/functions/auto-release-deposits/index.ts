import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Find completed bookings that need deposit release
    const now = new Date()
    const releaseThreshold = new Date(now.getTime() - (24 * 60 * 60 * 1000)) // 24 hours ago

    const { data: bookings, error } = await supabaseClient
      .from('bookings')
      .select(`
        booking_id,
        tech_id,
        total_amount_pence,
        deposit_pence,
        appointment_time,
        payment_accounts!inner (
          stripe_account_id
        )
      `)
      .eq('status', 'completed')
      .eq('payment_status', 'paid')
      .lt('appointment_time', releaseThreshold.toISOString())
      .is('auto_release_time', null) // Not yet processed

    if (error) {
      throw error
    }

    console.log(`Found ${bookings?.length || 0} bookings ready for deposit release`)

    let releasedCount = 0

    // Process each booking
    for (const booking of bookings || []) {
      try {
        await processDepositRelease(supabaseClient, booking)
        releasedCount++
      } catch (error) {
        console.error(`Error processing booking ${booking.booking_id}:`, error)
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        processed: bookings?.length || 0,
        released: releasedCount
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Auto-release deposits error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

async function processDepositRelease(supabase: any, booking: any) {
  const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')
  
  if (!stripeSecretKey) {
    throw new Error('Stripe secret key not configured')
  }

  // Calculate amounts
  const totalAmount = booking.total_amount_pence
  const depositAmount = booking.deposit_pence
  const remainingAmount = totalAmount - depositAmount
  
  // Calculate platform fee (e.g., 5% of total)
  const platformFeeRate = 0.05
  const platformFee = Math.round(totalAmount * platformFeeRate)
  const technicianPayout = totalAmount - platformFee

  try {
    // Create Stripe transfer to technician
    const transferResponse = await fetch('https://api.stripe.com/v1/transfers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeSecretKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        amount: technicianPayout.toString(),
        currency: 'gbp',
        destination: booking.payment_accounts.stripe_account_id,
        description: `Payout for booking ${booking.booking_id}`,
        metadata: JSON.stringify({
          booking_id: booking.booking_id,
          tech_id: booking.tech_id,
          original_amount: totalAmount.toString(),
          platform_fee: platformFee.toString(),
        }),
      }),
    })

    if (!transferResponse.ok) {
      const errorData = await transferResponse.json()
      throw new Error(`Stripe transfer failed: ${errorData.error?.message || 'Unknown error'}`)
    }

    const transfer = await transferResponse.json()

    // Update booking with release information
    const { error: updateError } = await supabase
      .from('bookings')
      .update({
        auto_release_time: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('booking_id', booking.booking_id)

    if (updateError) {
      throw updateError
    }

    // Create audit log
    const { error: auditError } = await supabase
      .from('audit_logs')
      .insert({
        user_id: booking.tech_id,
        action_type: 'deposit_auto_released',
        action_details: {
          booking_id: booking.booking_id,
          transfer_id: transfer.id,
          amount_released: technicianPayout,
          platform_fee: platformFee,
          original_amount: totalAmount,
        },
      })

    if (auditError) {
      console.error('Error creating audit log:', auditError)
    }

    // Create notification for technician
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: booking.tech_id,
        type: 'booking',
        message: `Payment of £${(technicianPayout / 100).toFixed(2)} has been transferred to your account for completed booking.`,
      })

    if (notificationError) {
      console.error('Error creating notification:', notificationError)
    }

    console.log(`Deposit released for booking ${booking.booking_id}: £${(technicianPayout / 100).toFixed(2)}`)

  } catch (error) {
    // Log the error but don't throw - we want to continue processing other bookings
    console.error(`Failed to release deposit for booking ${booking.booking_id}:`, error)
    
    // Create error audit log
    await supabase
      .from('audit_logs')
      .insert({
        user_id: booking.tech_id,
        action_type: 'deposit_release_failed',
        action_details: {
          booking_id: booking.booking_id,
          error: error.message,
          amount_attempted: technicianPayout,
        },
      })

    throw error
  }
}

// Helper function to calculate platform fees
function calculatePlatformFee(amount: number, rate: number = 0.05): number {
  return Math.round(amount * rate)
}

// Helper function to format currency
function formatCurrency(pence: number): string {
  return `£${(pence / 100).toFixed(2)}`
}
