import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get bookings that need reminders
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)

    const dayAfterTomorrow = new Date(tomorrow)
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1)

    // Get bookings for tomorrow that haven't been reminded
    const { data: bookings, error } = await supabaseClient
      .from('bookings')
      .select(`
        booking_id,
        appointment_time,
        users!customer_id (
          user_id,
          name,
          email
        ),
        services (
          name,
          duration_minutes
        ),
        beauty_technicians (
          studio_name,
          users (
            name
          )
        )
      `)
      .eq('status', 'confirmed')
      .gte('appointment_time', tomorrow.toISOString())
      .lt('appointment_time', dayAfterTomorrow.toISOString())

    if (error) {
      throw error
    }

    console.log(`Found ${bookings?.length || 0} bookings to remind`)

    // Send reminders
    for (const booking of bookings || []) {
      await sendBookingReminder(supabaseClient, booking)
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        reminders_sent: bookings?.length || 0 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Booking reminder error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

async function sendBookingReminder(supabase: any, booking: any) {
  const appointmentDate = new Date(booking.appointment_time)
  const formattedDate = appointmentDate.toLocaleDateString('en-GB', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
  const formattedTime = appointmentDate.toLocaleTimeString('en-GB', {
    hour: '2-digit',
    minute: '2-digit',
  })

  // Create notification in database
  const { error: notificationError } = await supabase
    .from('notifications')
    .insert({
      user_id: booking.users.user_id,
      type: 'reminder',
      message: `Reminder: You have a ${booking.services.name} appointment tomorrow at ${formattedTime} with ${booking.beauty_technicians.users.name} at ${booking.beauty_technicians.studio_name}.`,
    })

  if (notificationError) {
    console.error('Error creating notification:', notificationError)
  }

  // Send email reminder (if SendGrid is configured)
  if (Deno.env.get('SENDGRID_API_KEY')) {
    await sendEmailReminder(booking, formattedDate, formattedTime)
  }

  // Send push notification (if FCM is configured)
  if (Deno.env.get('FCM_SERVER_KEY')) {
    await sendPushNotification(booking, formattedDate, formattedTime)
  }

  console.log(`Reminder sent for booking ${booking.booking_id}`)
}

async function sendEmailReminder(booking: any, formattedDate: string, formattedTime: string) {
  const sendGridApiKey = Deno.env.get('SENDGRID_API_KEY')
  const fromEmail = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'

  const emailData = {
    personalizations: [{
      to: [{ email: booking.users.email, name: booking.users.name }],
      subject: 'Appointment Reminder - BeautyHub',
    }],
    from: { email: fromEmail, name: 'BeautyHub' },
    content: [{
      type: 'text/html',
      value: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #E91E63;">Appointment Reminder</h2>
          <p>Hi ${booking.users.name},</p>
          <p>This is a friendly reminder about your upcoming appointment:</p>
          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #333;">Appointment Details</h3>
            <p><strong>Service:</strong> ${booking.services.name}</p>
            <p><strong>Date:</strong> ${formattedDate}</p>
            <p><strong>Time:</strong> ${formattedTime}</p>
            <p><strong>Duration:</strong> ${booking.services.duration_minutes} minutes</p>
            <p><strong>Technician:</strong> ${booking.beauty_technicians.users.name}</p>
            <p><strong>Location:</strong> ${booking.beauty_technicians.studio_name}</p>
          </div>
          <p>We look forward to seeing you tomorrow!</p>
          <p>If you need to cancel or reschedule, please do so at least 24 hours in advance.</p>
          <p>Best regards,<br>The BeautyHub Team</p>
        </div>
      `
    }]
  }

  try {
    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${sendGridApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    })

    if (!response.ok) {
      throw new Error(`SendGrid API error: ${response.status}`)
    }

    console.log(`Email reminder sent to ${booking.users.email}`)
  } catch (error) {
    console.error('Error sending email reminder:', error)
  }
}

async function sendPushNotification(booking: any, formattedDate: string, formattedTime: string) {
  const fcmServerKey = Deno.env.get('FCM_SERVER_KEY')
  
  // In a real implementation, you'd need to get the user's FCM token from the database
  // For now, this is a placeholder
  
  const notificationData = {
    to: '/topics/user_' + booking.users.user_id, // Topic-based messaging
    notification: {
      title: 'Appointment Reminder',
      body: `${booking.services.name} appointment tomorrow at ${formattedTime}`,
      icon: 'ic_notification',
      sound: 'default',
    },
    data: {
      booking_id: booking.booking_id,
      type: 'appointment_reminder',
    },
  }

  try {
    const response = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Authorization': `key=${fcmServerKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notificationData),
    })

    if (!response.ok) {
      throw new Error(`FCM API error: ${response.status}`)
    }

    console.log(`Push notification sent for booking ${booking.booking_id}`)
  } catch (error) {
    console.error('Error sending push notification:', error)
  }
}
