-- BeautyHub Row Level Security Policies
-- Secure data access based on user roles and ownership

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE beauty_technicians ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID)
RETURNS user_role AS $$
BEGIN
    RETURN (SELECT role FROM users WHERE user_id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
R<PERSON><PERSON>NS BOOLEAN AS $$
BEGIN
    RETURN get_user_role(auth.uid()) = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = user_id OR is_admin());

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = user_id OR is_admin());

CREATE POLICY "Anyone can view public user info" ON users
    FOR SELECT USING (TRUE); -- Public profiles for discovery

CREATE POLICY "New users can insert their profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Beauty Technicians table policies
CREATE POLICY "Anyone can view tech profiles" ON beauty_technicians
    FOR SELECT USING (TRUE); -- Public for customer discovery

CREATE POLICY "Techs can update their own profile" ON beauty_technicians
    FOR UPDATE USING (auth.uid() = tech_id OR is_admin());

CREATE POLICY "Techs can insert their profile" ON beauty_technicians
    FOR INSERT WITH CHECK (auth.uid() = tech_id);

-- Services table policies
CREATE POLICY "Anyone can view active services" ON services
    FOR SELECT USING (is_active = TRUE OR auth.uid() = tech_id OR is_admin());

CREATE POLICY "Techs can manage their services" ON services
    FOR ALL USING (auth.uid() = tech_id OR is_admin());

CREATE POLICY "Techs can insert services" ON services
    FOR INSERT WITH CHECK (auth.uid() = tech_id);

-- Bookings table policies
CREATE POLICY "Users can view their bookings" ON bookings
    FOR SELECT USING (
        auth.uid() = customer_id OR 
        auth.uid() = tech_id OR 
        is_admin()
    );

CREATE POLICY "Customers can create bookings" ON bookings
    FOR INSERT WITH CHECK (auth.uid() = customer_id);

CREATE POLICY "Booking parties can update bookings" ON bookings
    FOR UPDATE USING (
        auth.uid() = customer_id OR 
        auth.uid() = tech_id OR 
        is_admin()
    );

-- Reviews table policies
CREATE POLICY "Anyone can view reviews" ON reviews
    FOR SELECT USING (TRUE); -- Public reviews for transparency

CREATE POLICY "Users can create reviews for their bookings" ON reviews
    FOR INSERT WITH CHECK (
        auth.uid() = reviewer_id AND
        EXISTS (
            SELECT 1 FROM bookings 
            WHERE booking_id = reviews.booking_id 
            AND (customer_id = auth.uid() OR tech_id = auth.uid())
            AND status = 'completed'
        )
    );

CREATE POLICY "Reviewers can update their reviews" ON reviews
    FOR UPDATE USING (auth.uid() = reviewer_id OR is_admin());

-- Notifications table policies
CREATE POLICY "Users can view their notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id OR is_admin());

CREATE POLICY "Users can update their notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id OR is_admin());

CREATE POLICY "System can insert notifications" ON notifications
    FOR INSERT WITH CHECK (TRUE); -- Allow system to create notifications

-- Account Settings table policies
CREATE POLICY "Users can view their settings" ON account_settings
    FOR SELECT USING (auth.uid() = user_id OR is_admin());

CREATE POLICY "Users can manage their settings" ON account_settings
    FOR ALL USING (auth.uid() = user_id OR is_admin());

CREATE POLICY "Users can insert their settings" ON account_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Payment Accounts table policies
CREATE POLICY "Techs can view their payment accounts" ON payment_accounts
    FOR SELECT USING (auth.uid() = tech_id OR is_admin());

CREATE POLICY "Techs can manage their payment accounts" ON payment_accounts
    FOR ALL USING (auth.uid() = tech_id OR is_admin());

CREATE POLICY "Techs can insert payment accounts" ON payment_accounts
    FOR INSERT WITH CHECK (auth.uid() = tech_id);

-- Audit Logs table policies
CREATE POLICY "Admins can view all audit logs" ON audit_logs
    FOR SELECT USING (is_admin());

CREATE POLICY "Users can view their audit logs" ON audit_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (TRUE); -- Allow system to log actions
