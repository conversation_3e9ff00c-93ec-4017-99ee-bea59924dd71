-- BeautyHub Database Indexes and Constraints
-- Performance optimization and data integrity

-- Indexes for better query performance

-- Users table indexes
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Beauty Technicians table indexes
CREATE INDEX idx_beauty_technicians_location ON beauty_technicians USING GIST(location);
CREATE INDEX idx_beauty_technicians_availability ON beauty_technicians(availability_status);
CREATE INDEX idx_beauty_technicians_verification ON beauty_technicians(verification_status);
CREATE INDEX idx_beauty_technicians_created_at ON beauty_technicians(created_at);

-- Services table indexes
CREATE INDEX idx_services_tech_id ON services(tech_id);
CREATE INDEX idx_services_category ON services(category);
CREATE INDEX idx_services_is_active ON services(is_active);
CREATE INDEX idx_services_price ON services(price_pence);
CREATE INDEX idx_services_created_at ON services(created_at);

-- Bookings table indexes
CREATE INDEX idx_bookings_customer_id ON bookings(customer_id);
CREATE INDEX idx_bookings_service_id ON bookings(service_id);
CREATE INDEX idx_bookings_tech_id ON bookings(tech_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_appointment_time ON bookings(appointment_time);
CREATE INDEX idx_bookings_payment_status ON bookings(payment_status);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);

-- Reviews table indexes
CREATE INDEX idx_reviews_booking_id ON reviews(booking_id);
CREATE INDEX idx_reviews_reviewer_id ON reviews(reviewer_id);
CREATE INDEX idx_reviews_reviewee_id ON reviews(reviewee_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_created_at ON reviews(created_at);

-- Notifications table indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read_status ON notifications(read_status);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Account Settings table indexes
CREATE INDEX idx_account_settings_user_id ON account_settings(user_id);

-- Payment Accounts table indexes
CREATE INDEX idx_payment_accounts_tech_id ON payment_accounts(tech_id);
CREATE INDEX idx_payment_accounts_stripe_id ON payment_accounts(stripe_account_id);

-- Audit Logs table indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action_type ON audit_logs(action_type);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Additional constraints and checks

-- Ensure booking appointment time is in the future (at creation)
ALTER TABLE bookings ADD CONSTRAINT chk_appointment_future 
    CHECK (appointment_time > created_at);

-- Ensure deposit is not more than total price
ALTER TABLE services ADD CONSTRAINT chk_deposit_reasonable 
    CHECK (deposit_pence <= price_pence);

-- Ensure positive prices
ALTER TABLE services ADD CONSTRAINT chk_positive_price 
    CHECK (price_pence > 0);

ALTER TABLE services ADD CONSTRAINT chk_positive_deposit 
    CHECK (deposit_pence >= 0);

ALTER TABLE bookings ADD CONSTRAINT chk_positive_total 
    CHECK (total_amount_pence > 0);

-- Ensure reasonable duration
ALTER TABLE services ADD CONSTRAINT chk_reasonable_duration 
    CHECK (duration_minutes > 0 AND duration_minutes <= 480); -- Max 8 hours

-- Composite indexes for common query patterns
CREATE INDEX idx_bookings_tech_status_time ON bookings(tech_id, status, appointment_time);
CREATE INDEX idx_services_tech_active_category ON services(tech_id, is_active, category);
CREATE INDEX idx_reviews_reviewee_rating ON reviews(reviewee_id, rating);
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, read_status) WHERE read_status = FALSE;

-- Partial indexes for better performance on filtered queries
CREATE INDEX idx_active_services ON services(tech_id, category) WHERE is_active = TRUE;
CREATE INDEX idx_pending_bookings ON bookings(tech_id, appointment_time) WHERE status = 'pending';
CREATE INDEX idx_available_techs ON beauty_technicians(location) WHERE availability_status = 'available';
