-- BeautyHub Storage Setup
-- Configure storage buckets and policies

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
    ('profile-images', 'profile-images', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
    ('service-images', 'service-images', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
    ('header-images', 'header-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
    ('certifications', 'certifications', false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'application/pdf']),
    ('review-media', 'review-media', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/quicktime']);

-- Storage policies for profile-images bucket
CREATE POLICY "Anyone can view profile images" ON storage.objects
    FOR SELECT USING (bucket_id = 'profile-images');

CREATE POLICY "Users can upload their own profile image" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'profile-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own profile image" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'profile-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own profile image" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'profile-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Storage policies for service-images bucket
CREATE POLICY "Anyone can view service images" ON storage.objects
    FOR SELECT USING (bucket_id = 'service-images');

CREATE POLICY "Techs can upload service images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'service-images' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

CREATE POLICY "Techs can update their service images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'service-images' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

CREATE POLICY "Techs can delete their service images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'service-images' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

-- Storage policies for header-images bucket
CREATE POLICY "Anyone can view header images" ON storage.objects
    FOR SELECT USING (bucket_id = 'header-images');

CREATE POLICY "Techs can upload header images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'header-images' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

CREATE POLICY "Techs can update their header images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'header-images' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

CREATE POLICY "Techs can delete their header images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'header-images' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

-- Storage policies for certifications bucket (private)
CREATE POLICY "Techs can view their own certifications" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'certifications' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Admins can view all certifications" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'certifications' AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Techs can upload certifications" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'certifications' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

CREATE POLICY "Techs can update their certifications" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'certifications' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

CREATE POLICY "Techs can delete their certifications" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'certifications' AND
        auth.uid()::text = (storage.foldername(name))[1] AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE user_id = auth.uid() AND role = 'tech'
        )
    );

-- Storage policies for review-media bucket
CREATE POLICY "Anyone can view review media" ON storage.objects
    FOR SELECT USING (bucket_id = 'review-media');

CREATE POLICY "Users can upload review media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'review-media' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their review media" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'review-media' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their review media" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'review-media' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );
