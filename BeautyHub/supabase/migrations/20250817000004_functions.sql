-- BeautyHub Database Functions
-- Common operations and business logic

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_beauty_technicians_updated_at BEFORE UPDATE ON beauty_technicians
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_account_settings_updated_at BEFORE UPDATE ON account_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_accounts_updated_at BEFORE UPDATE ON payment_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate distance between two points
CREATE OR REPLACE FUNCTION calculate_distance(
    lat1 DOUBLE PRECISION,
    lng1 DOUBLE PRECISION,
    lat2 DOUBLE PRECISION,
    lng2 DOUBLE PRECISION
)
RETURNS DOUBLE PRECISION AS $$
BEGIN
    RETURN ST_Distance(
        ST_GeogFromText('POINT(' || lng1 || ' ' || lat1 || ')'),
        ST_GeogFromText('POINT(' || lng2 || ' ' || lat2 || ')')
    ) / 1609.34; -- Convert meters to miles
END;
$$ LANGUAGE plpgsql;

-- Function to find nearby technicians
CREATE OR REPLACE FUNCTION find_nearby_technicians(
    user_lat DOUBLE PRECISION,
    user_lng DOUBLE PRECISION,
    radius_miles DOUBLE PRECISION DEFAULT 25
)
RETURNS TABLE (
    tech_id UUID,
    studio_name VARCHAR(255),
    distance_miles DOUBLE PRECISION,
    verification_status verification_status,
    availability_status availability_status
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bt.tech_id,
        bt.studio_name,
        ST_Distance(
            bt.location,
            ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')')
        ) / 1609.34 AS distance_miles,
        bt.verification_status,
        bt.availability_status
    FROM beauty_technicians bt
    WHERE ST_DWithin(
        bt.location,
        ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
        radius_miles * 1609.34 -- Convert miles to meters
    )
    AND bt.availability_status = 'available'
    ORDER BY distance_miles;
END;
$$ LANGUAGE plpgsql;

-- Function to get technician average rating
CREATE OR REPLACE FUNCTION get_tech_average_rating(tech_uuid UUID)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    avg_rating DECIMAL(3,2);
BEGIN
    SELECT ROUND(AVG(rating::DECIMAL), 2) INTO avg_rating
    FROM reviews
    WHERE reviewee_id = tech_uuid;
    
    RETURN COALESCE(avg_rating, 0.00);
END;
$$ LANGUAGE plpgsql;

-- Function to get technician review count
CREATE OR REPLACE FUNCTION get_tech_review_count(tech_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    review_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO review_count
    FROM reviews
    WHERE reviewee_id = tech_uuid;
    
    RETURN COALESCE(review_count, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to check booking availability
CREATE OR REPLACE FUNCTION check_booking_availability(
    tech_uuid UUID,
    service_uuid UUID,
    requested_time TIMESTAMP WITH TIME ZONE
)
RETURNS BOOLEAN AS $$
DECLARE
    service_duration INTEGER;
    conflict_count INTEGER;
BEGIN
    -- Get service duration
    SELECT duration_minutes INTO service_duration
    FROM services
    WHERE service_id = service_uuid;
    
    -- Check for conflicting bookings
    SELECT COUNT(*) INTO conflict_count
    FROM bookings
    WHERE tech_id = tech_uuid
    AND status IN ('pending', 'confirmed')
    AND (
        (appointment_time <= requested_time AND 
         appointment_time + INTERVAL '1 minute' * (
             SELECT duration_minutes FROM services WHERE service_id = bookings.service_id
         ) > requested_time)
        OR
        (requested_time <= appointment_time AND 
         requested_time + INTERVAL '1 minute' * service_duration > appointment_time)
    );
    
    RETURN conflict_count = 0;
END;
$$ LANGUAGE plpgsql;

-- Function to create audit log entry
CREATE OR REPLACE FUNCTION create_audit_log(
    user_uuid UUID,
    action VARCHAR(100),
    details JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    log_uuid UUID;
BEGIN
    INSERT INTO audit_logs (user_id, action_type, action_details)
    VALUES (user_uuid, action, details)
    RETURNING log_id INTO log_uuid;
    
    RETURN log_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (user_id, email, name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User'),
        COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'customer')
    );
    
    -- Create default account settings
    INSERT INTO account_settings (user_id)
    VALUES (NEW.id);
    
    -- Create audit log
    PERFORM create_audit_log(NEW.id, 'user_registered', 
        jsonb_build_object('email', NEW.email, 'role', COALESCE(NEW.raw_user_meta_data->>'role', 'customer'))
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
