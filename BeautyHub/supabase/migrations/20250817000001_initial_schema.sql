-- BeautyHub Database Schema
-- Initial migration for all core tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('customer', 'tech', 'admin');
CREATE TYPE availability_status AS ENUM ('available', 'unavailable');
CREATE TYPE verification_status AS ENUM ('unverified', 'verified', 'certified');
CREATE TYPE booking_status AS ENUM ('pending', 'confirmed', 'completed', 'cancelled', 'no_show');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'refunded');
CREATE TYPE notification_type AS ENUM ('booking', 'cancellation', 'reminder', 'marketing');
CREATE TYPE payout_method AS ENUM ('instant', 'weekly', 'monthly');

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    role user_role NOT NULL DEFAULT 'customer',
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone_number VARCHAR(20),
    profile_picture_url TEXT,
    language_preference VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beauty Technicians table
CREATE TABLE beauty_technicians (
    tech_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    studio_name VARCHAR(255),
    location GEOGRAPHY(POINT, 4326), -- PostGIS point for lat/lng
    header_image_url TEXT,
    bio TEXT,
    social_links JSONB DEFAULT '{}',
    working_hours JSONB DEFAULT '{}',
    availability_status availability_status DEFAULT 'available',
    verification_status verification_status DEFAULT 'unverified',
    certifications JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Services table
CREATE TABLE services (
    service_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tech_id UUID NOT NULL REFERENCES beauty_technicians(tech_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    duration_minutes INTEGER NOT NULL,
    price_pence INTEGER NOT NULL, -- Stored in pence, displayed as £
    deposit_pence INTEGER NOT NULL,
    add_ons JSONB DEFAULT '[]',
    faqs JSONB DEFAULT '[]',
    images JSONB DEFAULT '[]',
    video_urls JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookings table
CREATE TABLE bookings (
    booking_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    service_id UUID NOT NULL REFERENCES services(service_id) ON DELETE CASCADE,
    tech_id UUID NOT NULL REFERENCES beauty_technicians(tech_id) ON DELETE CASCADE,
    status booking_status DEFAULT 'pending',
    appointment_time TIMESTAMP WITH TIME ZONE NOT NULL,
    deposit_paid BOOLEAN DEFAULT FALSE,
    auto_release_time TIMESTAMP WITH TIME ZONE,
    total_amount_pence INTEGER NOT NULL,
    payment_status payment_status DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reviews table
CREATE TABLE reviews (
    review_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID NOT NULL REFERENCES bookings(booking_id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    reviewee_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    media JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
    notification_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    message TEXT NOT NULL,
    read_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Account Settings table
CREATE TABLE account_settings (
    settings_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE UNIQUE,
    linked_devices JSONB DEFAULT '[]',
    communication_preferences JSONB DEFAULT '{}',
    privacy_settings JSONB DEFAULT '{}',
    dark_mode BOOLEAN DEFAULT FALSE,
    accessibility_options JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment Accounts table
CREATE TABLE payment_accounts (
    payment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tech_id UUID NOT NULL REFERENCES beauty_technicians(tech_id) ON DELETE CASCADE UNIQUE,
    stripe_account_id VARCHAR(255),
    payout_method payout_method DEFAULT 'weekly',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit Logs table
CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    action_type VARCHAR(100) NOT NULL,
    action_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
