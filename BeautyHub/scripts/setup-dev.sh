#!/bin/bash

# BeautyHub Development Setup Script
# This script helps set up the local development environment

set -e

echo "🚀 BeautyHub Development Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js found: $NODE_VERSION"
else
    print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "npm found: $NPM_VERSION"
else
    print_error "npm is not installed. Please install npm."
    exit 1
fi

# Check Supabase CLI
if command_exists supabase; then
    SUPABASE_VERSION=$(supabase --version)
    print_success "Supabase CLI found: $SUPABASE_VERSION"
else
    print_warning "Supabase CLI not found. Installing..."
    npm install -g supabase
    print_success "Supabase CLI installed"
fi

# Check Docker
if command_exists docker; then
    print_success "Docker found"
    if docker info >/dev/null 2>&1; then
        print_success "Docker daemon is running"
    else
        print_warning "Docker daemon is not running. Please start Docker Desktop."
        print_status "You can start Docker and run this script again, or continue without local Supabase."
        read -p "Continue without local Supabase? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
        SKIP_SUPABASE=true
    fi
else
    print_warning "Docker not found. Local Supabase development requires Docker."
    print_status "Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
    read -p "Continue without local Supabase? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    SKIP_SUPABASE=true
fi

# Install dependencies
print_status "Installing npm dependencies..."
npm install
print_success "Dependencies installed"

# Setup environment file
print_status "Setting up environment configuration..."
if [ ! -f .env ]; then
    cp .env.example .env
    print_success "Created .env file from .env.example"
    print_warning "Please edit .env file with your actual configuration values"
else
    print_warning ".env file already exists. Please ensure it's properly configured."
fi

# Setup Supabase (if Docker is available)
if [ "$SKIP_SUPABASE" != "true" ]; then
    print_status "Setting up local Supabase..."
    
    # Start Supabase
    print_status "Starting Supabase services..."
    supabase start
    
    if [ $? -eq 0 ]; then
        print_success "Supabase started successfully"
        
        # Get the local configuration
        print_status "Getting local Supabase configuration..."
        supabase status
        
        print_success "Local Supabase is ready!"
        print_status "You can access:"
        print_status "  - API: http://127.0.0.1:54321"
        print_status "  - Studio: http://127.0.0.1:54323"
        print_status "  - Inbucket (Email testing): http://127.0.0.1:54324"
    else
        print_error "Failed to start Supabase"
        exit 1
    fi
else
    print_warning "Skipping local Supabase setup"
    print_status "You'll need to configure a remote Supabase project in your .env file"
fi

# iOS setup (if on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_status "Setting up iOS dependencies..."
    if command_exists pod; then
        cd ios && pod install && cd ..
        print_success "iOS pods installed"
    else
        print_warning "CocoaPods not found. Please install it to run on iOS:"
        print_status "  sudo gem install cocoapods"
    fi
fi

# Final instructions
echo
print_success "🎉 Setup complete!"
echo
print_status "Next steps:"
print_status "1. Edit .env file with your configuration"
if [ "$SKIP_SUPABASE" != "true" ]; then
    print_status "2. Your local Supabase is running with sample data"
    print_status "3. Run 'npm run ios' or 'npm run android' to start the app"
else
    print_status "2. Set up a remote Supabase project and update .env"
    print_status "3. Run 'npm run ios' or 'npm run android' to start the app"
fi
print_status "4. Access Supabase Studio at http://127.0.0.1:54323 (if running locally)"
echo
print_status "For more information, check the README.md file"
echo
print_success "Happy coding! 🚀"
