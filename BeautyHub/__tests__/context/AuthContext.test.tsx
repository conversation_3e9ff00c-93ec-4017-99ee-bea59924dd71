/**
 * Tests for AuthContext
 */

import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { AuthProvider, useAuth } from '../../src/context/AuthContext';
import { UserRole } from '../../src/types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock auth service
jest.mock('../../src/services/authService', () => ({
  authService: {
    login: jest.fn(),
    register: jest.fn(),
    validateToken: jest.fn(),
  },
}));

// Test component to access auth context
const TestComponent: React.FC = () => {
  const auth = useAuth();
  return null; // We're just testing the context, not rendering
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('provides auth context to children', () => {
    expect(() => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    }).not.toThrow();
  });

  it('throws error when useAuth is used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');
    
    consoleSpy.mockRestore();
  });

  it('initializes with correct default state', async () => {
    let authState: any;
    
    const TestComponent: React.FC = () => {
      authState = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(authState.user).toBeNull();
      expect(authState.token).toBeNull();
      expect(authState.error).toBeNull();
      expect(typeof authState.login).toBe('function');
      expect(typeof authState.register).toBe('function');
      expect(typeof authState.logout).toBe('function');
    });
  });

  // Add more specific tests for login, register, logout functions
  // These would require more detailed mocking of the auth service
});
