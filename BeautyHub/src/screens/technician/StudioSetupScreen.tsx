import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';

interface StudioData {
  name: string;
  description: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  amenities: string[];
  policies: {
    cancellationPolicy: string;
    depositPolicy: string;
    latePolicyMinutes: number;
    noShowPolicy: string;
  };
}

const StudioSetupScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [formData, setFormData] = useState<StudioData>({
    name: '',
    description: '',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    amenities: [],
    policies: {
      cancellationPolicy: '',
      depositPolicy: '',
      latePolicyMinutes: 15,
      noShowPolicy: '',
    },
  });
  
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const amenityOptions = [
    'Parking Available', 'Wheelchair Accessible', 'WiFi', 'Refreshments',
    'Music System', 'Air Conditioning', 'Natural Lighting', 'Private Room',
    'Sanitization Station', 'Waiting Area'
  ];

  const handleInputChange = (field: string, value: string | number) => {
    if (field.startsWith('policies.')) {
      const policyField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        policies: {
          ...prev.policies,
          [policyField]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const toggleAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleComplete = () => {
    // TODO: Save studio data to backend
    Alert.alert(
      'Studio Setup Complete!',
      'Your studio information has been saved successfully.',
      [
        {
          text: 'Continue',
          onPress: () => navigation.navigate('WorkingHoursSetup' as never),
        },
      ]
    );
  };

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { width: `${(currentStep / totalSteps) * 100}%` }
          ]} 
        />
      </View>
      <Text style={styles.progressText}>
        Step {currentStep} of {totalSteps}
      </Text>
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Studio Information</Text>
      <Text style={styles.stepSubtitle}>
        Tell us about your workspace
      </Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Studio Name</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your studio name"
          value={formData.name}
          onChangeText={(value) => handleInputChange('name', value)}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Description</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="Describe your studio atmosphere, style, and what makes it special..."
          value={formData.description}
          onChangeText={(value) => handleInputChange('description', value)}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Studio Location</Text>
      <Text style={styles.stepSubtitle}>
        Where is your studio located?
      </Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Street Address</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter street address"
          value={formData.street}
          onChangeText={(value) => handleInputChange('street', value)}
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputContainer, styles.flex1]}>
          <Text style={styles.label}>City</Text>
          <TextInput
            style={styles.input}
            placeholder="City"
            value={formData.city}
            onChangeText={(value) => handleInputChange('city', value)}
          />
        </View>

        <View style={[styles.inputContainer, styles.flex1]}>
          <Text style={styles.label}>State</Text>
          <TextInput
            style={styles.input}
            placeholder="State"
            value={formData.state}
            onChangeText={(value) => handleInputChange('state', value)}
          />
        </View>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>ZIP Code</Text>
        <TextInput
          style={[styles.input, styles.zipInput]}
          placeholder="ZIP Code"
          value={formData.zipCode}
          onChangeText={(value) => handleInputChange('zipCode', value)}
          keyboardType="numeric"
        />
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Studio Amenities</Text>
      <Text style={styles.stepSubtitle}>
        What amenities does your studio offer?
      </Text>

      <View style={styles.amenitiesContainer}>
        {amenityOptions.map((amenity) => (
          <TouchableOpacity
            key={amenity}
            style={[
              styles.amenityItem,
              formData.amenities.includes(amenity) && styles.amenityItemSelected
            ]}
            onPress={() => toggleAmenity(amenity)}
          >
            <View style={[
              styles.checkbox,
              formData.amenities.includes(amenity) && styles.checkboxSelected
            ]}>
              {formData.amenities.includes(amenity) && (
                <Icon name="checkmark" size={16} color="#FFFFFF" />
              )}
            </View>
            <Text style={[
              styles.amenityText,
              formData.amenities.includes(amenity) && styles.amenityTextSelected
            ]}>
              {amenity}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Studio Policies</Text>
      <Text style={styles.stepSubtitle}>
        Set your booking and cancellation policies
      </Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Cancellation Policy</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="e.g., 24-hour cancellation notice required..."
          value={formData.policies.cancellationPolicy}
          onChangeText={(value) => handleInputChange('policies.cancellationPolicy', value)}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Late Policy (Minutes)</Text>
        <TextInput
          style={[styles.input, styles.numberInput]}
          placeholder="15"
          value={formData.policies.latePolicyMinutes.toString()}
          onChangeText={(value) => handleInputChange('policies.latePolicyMinutes', parseInt(value) || 0)}
          keyboardType="numeric"
        />
        <Text style={styles.helperText}>
          How many minutes late before considering it a no-show?
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>No-Show Policy</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="e.g., No-show appointments will be charged 50% of service fee..."
          value={formData.policies.noShowPolicy}
          onChangeText={(value) => handleInputChange('policies.noShowPolicy', value)}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      case 4: return renderStep4();
      default: return renderStep1();
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1: return formData.name.trim() && formData.description.trim();
      case 2: return formData.street.trim() && formData.city.trim() && 
                     formData.state.trim() && formData.zipCode.trim();
      case 3: return true; // Amenities are optional
      case 4: return formData.policies.cancellationPolicy.trim() && 
                     formData.policies.noShowPolicy.trim();
      default: return false;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="arrow-back" size={24} color="#2C3E50" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Studio Setup</Text>
        <View style={styles.placeholder} />
      </View>

      {renderProgressBar()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.nextButton, !isStepValid() && styles.nextButtonDisabled]}
          onPress={handleNext}
          disabled={!isStepValid()}
        >
          <Text style={[styles.nextButtonText, !isStepValid() && styles.nextButtonTextDisabled]}>
            {currentStep === totalSteps ? 'Complete' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  placeholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E8E8E8',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FF6B9D',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#7F8C8D',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  stepContainer: {
    paddingVertical: 20,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 32,
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  flex1: {
    flex: 1,
  },
  zipInput: {
    width: 120,
  },
  numberInput: {
    width: 100,
  },
  helperText: {
    fontSize: 14,
    color: '#7F8C8D',
    marginTop: 4,
  },
  amenitiesContainer: {
    gap: 12,
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E8E8E8',
    backgroundColor: '#FFFFFF',
  },
  amenityItemSelected: {
    backgroundColor: '#F8F9FA',
    borderColor: '#FF6B9D',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E8E8E8',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#FF6B9D',
    borderColor: '#FF6B9D',
  },
  amenityText: {
    fontSize: 16,
    color: '#2C3E50',
    flex: 1,
  },
  amenityTextSelected: {
    color: '#2C3E50',
    fontWeight: '500',
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  nextButton: {
    backgroundColor: '#FF6B9D',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  nextButtonDisabled: {
    backgroundColor: '#E8E8E8',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  nextButtonTextDisabled: {
    color: '#BDC3C7',
  },
});

export default StudioSetupScreen;
