import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';

interface DaySchedule {
  isWorking: boolean;
  startTime: string;
  endTime: string;
  breaks: { startTime: string; endTime: string; }[];
}

interface WorkingHours {
  [key: string]: DaySchedule;
}

const WorkingHoursSetupScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const daysOfWeek = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' },
  ];

  const timeSlots = [
    '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30',
    '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
    '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30',
    '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30',
    '22:00'
  ];

  const [workingHours, setWorkingHours] = useState<WorkingHours>({
    monday: { isWorking: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    thursday: { isWorking: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    friday: { isWorking: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    saturday: { isWorking: true, startTime: '10:00', endTime: '16:00', breaks: [] },
    sunday: { isWorking: false, startTime: '09:00', endTime: '17:00', breaks: [] },
  });

  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [selectedTimeType, setSelectedTimeType] = useState<'start' | 'end' | null>(null);

  const toggleDayWorking = (day: string) => {
    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        isWorking: !prev[day].isWorking,
      },
    }));
  };

  const updateTime = (day: string, timeType: 'start' | 'end', time: string) => {
    const field = timeType === 'start' ? 'startTime' : 'endTime';
    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: time,
      },
    }));
    setSelectedDay(null);
    setSelectedTimeType(null);
  };

  const copyToAllDays = (sourceDay: string) => {
    const sourceSchedule = workingHours[sourceDay];
    const updatedHours = { ...workingHours };
    
    daysOfWeek.forEach(({ key }) => {
      if (key !== sourceDay) {
        updatedHours[key] = {
          ...sourceSchedule,
          breaks: [...sourceSchedule.breaks],
        };
      }
    });
    
    setWorkingHours(updatedHours);
    Alert.alert('Success', 'Schedule copied to all days!');
  };

  const handleComplete = () => {
    // TODO: Save working hours to backend
    Alert.alert(
      'Working Hours Set!',
      'Your working hours have been saved successfully.',
      [
        {
          text: 'Continue to Dashboard',
          onPress: () => navigation.navigate('Main' as never),
        },
      ]
    );
  };

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const renderDaySchedule = (day: { key: string; label: string }) => {
    const schedule = workingHours[day.key];
    
    return (
      <View key={day.key} style={styles.dayContainer}>
        <View style={styles.dayHeader}>
          <Text style={styles.dayLabel}>{day.label}</Text>
          <Switch
            value={schedule.isWorking}
            onValueChange={() => toggleDayWorking(day.key)}
            trackColor={{ false: '#E8E8E8', true: '#FF6B9D' }}
            thumbColor={schedule.isWorking ? '#FFFFFF' : '#FFFFFF'}
          />
        </View>
        
        {schedule.isWorking && (
          <View style={styles.timeContainer}>
            <TouchableOpacity
              style={styles.timeButton}
              onPress={() => {
                setSelectedDay(day.key);
                setSelectedTimeType('start');
              }}
            >
              <Text style={styles.timeLabel}>Start</Text>
              <Text style={styles.timeValue}>{formatTime(schedule.startTime)}</Text>
            </TouchableOpacity>
            
            <Icon name="arrow-forward" size={16} color="#7F8C8D" />
            
            <TouchableOpacity
              style={styles.timeButton}
              onPress={() => {
                setSelectedDay(day.key);
                setSelectedTimeType('end');
              }}
            >
              <Text style={styles.timeLabel}>End</Text>
              <Text style={styles.timeValue}>{formatTime(schedule.endTime)}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.copyButton}
              onPress={() => copyToAllDays(day.key)}
            >
              <Icon name="copy-outline" size={16} color="#FF6B9D" />
            </TouchableOpacity>
          </View>
        )}
        
        {!schedule.isWorking && (
          <Text style={styles.closedText}>Closed</Text>
        )}
      </View>
    );
  };

  const renderTimeSelector = () => {
    if (!selectedDay || !selectedTimeType) return null;

    return (
      <View style={styles.timeSelectorOverlay}>
        <View style={styles.timeSelectorContainer}>
          <View style={styles.timeSelectorHeader}>
            <Text style={styles.timeSelectorTitle}>
              Select {selectedTimeType === 'start' ? 'Start' : 'End'} Time
            </Text>
            <TouchableOpacity
              onPress={() => {
                setSelectedDay(null);
                setSelectedTimeType(null);
              }}
            >
              <Icon name="close" size={24} color="#2C3E50" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.timeSlotsList}>
            {timeSlots.map((time) => (
              <TouchableOpacity
                key={time}
                style={styles.timeSlot}
                onPress={() => updateTime(selectedDay, selectedTimeType, time)}
              >
                <Text style={styles.timeSlotText}>{formatTime(time)}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#2C3E50" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Working Hours</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.introContainer}>
          <Text style={styles.title}>Set Your Availability</Text>
          <Text style={styles.subtitle}>
            Let clients know when you're available for bookings
          </Text>
        </View>

        <View style={styles.scheduleContainer}>
          {daysOfWeek.map(renderDaySchedule)}
        </View>

        <View style={styles.tipContainer}>
          <Icon name="bulb-outline" size={20} color="#FF6B9D" />
          <Text style={styles.tipText}>
            Tip: Use the copy button to apply the same schedule to all days
          </Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity style={styles.completeButton} onPress={handleComplete}>
          <Text style={styles.completeButtonText}>Complete Setup</Text>
        </TouchableOpacity>
      </View>

      {renderTimeSelector()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  introContainer: {
    paddingVertical: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    lineHeight: 22,
  },
  scheduleContainer: {
    marginBottom: 24,
  },
  dayContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dayLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  timeButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E8E8E8',
  },
  timeLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    marginBottom: 4,
  },
  timeValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
  },
  copyButton: {
    width: 40,
    height: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FF6B9D',
  },
  closedText: {
    fontSize: 16,
    color: '#7F8C8D',
    fontStyle: 'italic',
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5F8',
    padding: 16,
    borderRadius: 12,
    gap: 12,
    marginBottom: 24,
  },
  tipText: {
    fontSize: 14,
    color: '#7F8C8D',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  completeButton: {
    backgroundColor: '#FF6B9D',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  timeSelectorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timeSelectorContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    margin: 24,
    maxHeight: '70%',
    width: '80%',
  },
  timeSelectorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  timeSelectorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  timeSlotsList: {
    maxHeight: 300,
  },
  timeSlot: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  timeSlotText: {
    fontSize: 16,
    color: '#2C3E50',
  },
});

export default WorkingHoursSetupScreen;
