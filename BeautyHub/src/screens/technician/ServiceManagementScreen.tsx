import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Service, ServiceCategory } from '../../types';

const ServiceManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with API call
  useEffect(() => {
    const mockServices: Service[] = [
      {
        id: '1',
        technicianId: 'tech1',
        name: 'Classic Manicure',
        description: 'A relaxing classic manicure with nail shaping, cuticle care, and polish application.',
        category: ServiceCategory.NAILS,
        duration: 45,
        price: 35,
        images: ['https://via.placeholder.com/300x200/FF6B9D/FFFFFF?text=Manicure'],
        requirements: ['Clean nails', 'No recent nail injuries'],
        isActive: true,
        faqs: [
          {
            question: 'How long does it last?',
            answer: 'Typically 1-2 weeks with proper care.'
          }
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        technicianId: 'tech1',
        name: 'Gel Manicure',
        description: 'Long-lasting gel manicure with UV curing for extended wear.',
        category: ServiceCategory.NAILS,
        duration: 60,
        price: 55,
        images: ['https://via.placeholder.com/300x200/9B59B6/FFFFFF?text=Gel+Manicure'],
        requirements: ['Clean nails'],
        isActive: true,
        faqs: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '3',
        technicianId: 'tech1',
        name: 'Eyebrow Shaping',
        description: 'Professional eyebrow shaping and grooming.',
        category: ServiceCategory.BROWS,
        duration: 30,
        price: 25,
        images: ['https://via.placeholder.com/300x200/3498DB/FFFFFF?text=Eyebrows'],
        requirements: [],
        isActive: false,
        faqs: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
    
    setTimeout(() => {
      setServices(mockServices);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddService = () => {
    navigation.navigate('AddEditService' as never, { mode: 'add' } as never);
  };

  const handleEditService = (service: Service) => {
    navigation.navigate('AddEditService' as never, { mode: 'edit', service } as never);
  };

  const handleToggleService = (serviceId: string) => {
    setServices(prev => prev.map(service => 
      service.id === serviceId 
        ? { ...service, isActive: !service.isActive }
        : service
    ));
  };

  const handleDeleteService = (serviceId: string) => {
    Alert.alert(
      'Delete Service',
      'Are you sure you want to delete this service? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setServices(prev => prev.filter(service => service.id !== serviceId));
          },
        },
      ]
    );
  };

  const getCategoryIcon = (category: ServiceCategory) => {
    switch (category) {
      case ServiceCategory.NAILS: return 'hand-left-outline';
      case ServiceCategory.HAIR: return 'cut-outline';
      case ServiceCategory.MAKEUP: return 'color-palette-outline';
      case ServiceCategory.SKINCARE: return 'flower-outline';
      case ServiceCategory.LASHES: return 'eye-outline';
      case ServiceCategory.BROWS: return 'remove-outline';
      case ServiceCategory.MASSAGE: return 'hand-right-outline';
      default: return 'ellipsis-horizontal-outline';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  };

  const renderServiceCard = (service: Service) => (
    <View key={service.id} style={[styles.serviceCard, !service.isActive && styles.inactiveCard]}>
      <View style={styles.serviceHeader}>
        <View style={styles.serviceInfo}>
          <View style={styles.categoryIcon}>
            <Icon name={getCategoryIcon(service.category)} size={20} color="#FF6B9D" />
          </View>
          <View style={styles.serviceDetails}>
            <Text style={[styles.serviceName, !service.isActive && styles.inactiveText]}>
              {service.name}
            </Text>
            <Text style={styles.serviceCategory}>
              {service.category.charAt(0).toUpperCase() + service.category.slice(1)}
            </Text>
          </View>
        </View>
        
        <View style={styles.serviceActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleToggleService(service.id)}
          >
            <Icon 
              name={service.isActive ? 'eye' : 'eye-off'} 
              size={20} 
              color={service.isActive ? '#2ECC71' : '#7F8C8D'} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditService(service)}
          >
            <Icon name="create-outline" size={20} color="#3498DB" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteService(service.id)}
          >
            <Icon name="trash-outline" size={20} color="#E74C3C" />
          </TouchableOpacity>
        </View>
      </View>

      <Text style={[styles.serviceDescription, !service.isActive && styles.inactiveText]}>
        {service.description}
      </Text>

      <View style={styles.serviceFooter}>
        <View style={styles.serviceMeta}>
          <View style={styles.metaItem}>
            <Icon name="time-outline" size={16} color="#7F8C8D" />
            <Text style={styles.metaText}>{formatDuration(service.duration)}</Text>
          </View>
          
          <View style={styles.metaItem}>
            <Icon name="card-outline" size={16} color="#7F8C8D" />
            <Text style={styles.metaText}>${service.price}</Text>
          </View>
        </View>
        
        {!service.isActive && (
          <View style={styles.inactiveLabel}>
            <Text style={styles.inactiveLabelText}>Inactive</Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="cut-outline" size={64} color="#BDC3C7" />
      <Text style={styles.emptyTitle}>No Services Yet</Text>
      <Text style={styles.emptySubtitle}>
        Add your first service to start accepting bookings
      </Text>
      <TouchableOpacity style={styles.emptyButton} onPress={handleAddService}>
        <Text style={styles.emptyButtonText}>Add Service</Text>
      </TouchableOpacity>
    </View>
  );

  const activeServices = services.filter(s => s.isActive);
  const inactiveServices = services.filter(s => !s.isActive);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#2C3E50" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Services</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddService}>
          <Icon name="add" size={24} color="#FF6B9D" />
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading services...</Text>
        </View>
      ) : services.length === 0 ? (
        renderEmptyState()
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{activeServices.length}</Text>
              <Text style={styles.statLabel}>Active Services</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{inactiveServices.length}</Text>
              <Text style={styles.statLabel}>Inactive Services</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>
                ${Math.round(activeServices.reduce((sum, s) => sum + s.price, 0) / activeServices.length || 0)}
              </Text>
              <Text style={styles.statLabel}>Avg. Price</Text>
            </View>
          </View>

          {activeServices.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Active Services</Text>
              {activeServices.map(renderServiceCard)}
            </View>
          )}

          {inactiveServices.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Inactive Services</Text>
              {inactiveServices.map(renderServiceCard)}
            </View>
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  addButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#7F8C8D',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  emptyButton: {
    backgroundColor: '#FF6B9D',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 20,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B9D',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 16,
  },
  serviceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E8E8E8',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inactiveCard: {
    backgroundColor: '#F8F9FA',
    opacity: 0.7,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  serviceInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#FFF5F8',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  serviceDetails: {
    flex: 1,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 4,
  },
  serviceCategory: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  inactiveText: {
    color: '#BDC3C7',
  },
  serviceActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceDescription: {
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 20,
    marginBottom: 16,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  serviceMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  inactiveLabel: {
    backgroundColor: '#E8E8E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  inactiveLabelText: {
    fontSize: 12,
    color: '#7F8C8D',
    fontWeight: '500',
  },
});

export default ServiceManagementScreen;
