import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList, UserRole } from '../../types';
import Icon from 'react-native-vector-icons/Ionicons';

type RoleSelectionNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'RoleSelection'>;

const { width } = Dimensions.get('window');

interface RoleOption {
  role: UserRole;
  title: string;
  description: string;
  icon: string;
  color: string;
  benefits: string[];
}

const roleOptions: RoleOption[] = [
  {
    role: UserRole.CUSTOMER,
    title: 'I\'m looking for beauty services',
    description: 'Find and book appointments with top-rated beauty technicians',
    icon: 'person-outline',
    color: '#FF6B9D',
    benefits: [
      'Browse verified technicians',
      'Read reviews and ratings',
      'Secure online booking',
      'Flexible scheduling',
    ],
  },
  {
    role: UserRole.TECHNICIAN,
    title: 'I\'m a beauty professional',
    description: 'Showcase your skills and grow your beauty business',
    icon: 'cut-outline',
    color: '#9B59B6',
    benefits: [
      'Create your professional profile',
      'Manage your services and pricing',
      'Accept bookings instantly',
      'Grow your client base',
    ],
  },
];

const RoleSelectionScreen: React.FC = () => {
  const navigation = useNavigation<RoleSelectionNavigationProp>();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
  };

  const handleContinue = () => {
    if (selectedRole) {
      // Navigate to register with the selected role
      navigation.navigate('Register', { role: selectedRole } as any);
    }
  };

  const renderRoleCard = (option: RoleOption) => {
    const isSelected = selectedRole === option.role;
    
    return (
      <TouchableOpacity
        key={option.role}
        style={[
          styles.roleCard,
          isSelected && { borderColor: option.color, borderWidth: 2 },
        ]}
        onPress={() => handleRoleSelect(option.role)}
        activeOpacity={0.8}
      >
        <View style={styles.roleHeader}>
          <View style={[styles.iconContainer, { backgroundColor: option.color }]}>
            <Icon name={option.icon} size={32} color="#FFFFFF" />
          </View>
          
          {isSelected && (
            <View style={[styles.checkmark, { backgroundColor: option.color }]}>
              <Icon name="checkmark" size={16} color="#FFFFFF" />
            </View>
          )}
        </View>
        
        <Text style={styles.roleTitle}>{option.title}</Text>
        <Text style={styles.roleDescription}>{option.description}</Text>
        
        <View style={styles.benefitsList}>
          {option.benefits.map((benefit, index) => (
            <View key={index} style={styles.benefitItem}>
              <Icon name="checkmark-circle" size={16} color={option.color} />
              <Text style={styles.benefitText}>{benefit}</Text>
            </View>
          ))}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Welcome to BeautyHub</Text>
        <Text style={styles.headerSubtitle}>
          Choose how you'd like to use our platform
        </Text>
      </View>

      <View style={styles.content}>
        {roleOptions.map(renderRoleCard)}
      </View>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            !selectedRole && styles.continueButtonDisabled,
          ]}
          onPress={handleContinue}
          disabled={!selectedRole}
        >
          <Text style={[
            styles.continueButtonText,
            !selectedRole && styles.continueButtonTextDisabled,
          ]}>
            Continue
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.loginLink}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={styles.loginLinkText}>
            Already have an account? <Text style={styles.loginLinkTextBold}>Sign In</Text>
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  roleCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E8E8E8',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  roleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  roleTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
  },
  roleDescription: {
    fontSize: 16,
    color: '#7F8C8D',
    lineHeight: 22,
    marginBottom: 20,
  },
  benefitsList: {
    gap: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  benefitText: {
    fontSize: 14,
    color: '#34495E',
    flex: 1,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  continueButton: {
    backgroundColor: '#FF6B9D',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  continueButtonDisabled: {
    backgroundColor: '#E8E8E8',
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  continueButtonTextDisabled: {
    color: '#BDC3C7',
  },
  loginLink: {
    alignItems: 'center',
  },
  loginLinkText: {
    fontSize: 16,
    color: '#7F8C8D',
  },
  loginLinkTextBold: {
    color: '#FF6B9D',
    fontWeight: '600',
  },
});

export default RoleSelectionScreen;
