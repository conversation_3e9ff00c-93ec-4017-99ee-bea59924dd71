import { dbHelpers } from './supabase';
import { Database } from '../types/database';

type BeautyTechnician = Database['public']['Tables']['beauty_technicians']['Row'];
type Service = Database['public']['Tables']['services']['Row'];

class TechnicianService {
  // Get technician profile
  async getTechnician(techId: string): Promise<any> {
    try {
      const { data, error } = await dbHelpers.getTechnician(techId);
      
      if (error || !data) {
        throw new Error(error?.message || 'Technician not found');
      }
      
      return data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch technician');
    }
  }

  // Find nearby technicians
  async findNearbyTechnicians(
    latitude: number,
    longitude: number,
    radiusMiles: number = 25
  ): Promise<any[]> {
    try {
      const { data, error } = await dbHelpers.getNearbyTechnicians(latitude, longitude, radiusMiles);
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data || [];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to find nearby technicians');
    }
  }

  // Get technician services
  async getTechnicianServices(techId: string): Promise<Service[]> {
    try {
      const { data, error } = await dbHelpers.getServices(techId);
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data || [];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch services');
    }
  }

  // Get technician reviews
  async getTechnicianReviews(techId: string): Promise<any[]> {
    try {
      const { data, error } = await dbHelpers.getTechnicianReviews(techId);
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data || [];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch reviews');
    }
  }

  // Get technician average rating
  async getTechnicianRating(techId: string): Promise<{ average: number; count: number }> {
    try {
      const { data: average, error: avgError } = await dbHelpers.supabase.rpc('get_tech_average_rating', {
        tech_uuid: techId,
      });
      
      const { data: count, error: countError } = await dbHelpers.supabase.rpc('get_tech_review_count', {
        tech_uuid: techId,
      });
      
      if (avgError || countError) {
        throw new Error('Failed to fetch rating data');
      }
      
      return {
        average: average || 0,
        count: count || 0,
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch rating');
    }
  }

  // Search technicians by category
  async searchTechniciansByCategory(category: string): Promise<any[]> {
    try {
      const { data, error } = await dbHelpers.getServices(undefined, category);
      
      if (error) {
        throw new Error(error.message);
      }
      
      // Group services by technician
      const technicianMap = new Map();
      
      data?.forEach((service: any) => {
        const techId = service.tech_id;
        if (!technicianMap.has(techId)) {
          technicianMap.set(techId, {
            ...service.beauty_technicians,
            services: [],
          });
        }
        technicianMap.get(techId).services.push(service);
      });
      
      return Array.from(technicianMap.values());
    } catch (error: any) {
      throw new Error(error.message || 'Failed to search technicians');
    }
  }

  // Get available time slots for a technician
  async getAvailableTimeSlots(
    techId: string,
    date: string,
    serviceDuration: number
  ): Promise<string[]> {
    try {
      // This is a simplified version - in a real app, you'd want to:
      // 1. Get technician's working hours for the day
      // 2. Get existing bookings for the day
      // 3. Calculate available slots based on service duration
      
      const workingHours = {
        start: '09:00',
        end: '17:00',
      };
      
      const slots: string[] = [];
      const startHour = 9;
      const endHour = 17;
      const slotDuration = 30; // 30-minute slots
      
      for (let hour = startHour; hour < endHour; hour++) {
        for (let minute = 0; minute < 60; minute += slotDuration) {
          const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          slots.push(timeSlot);
        }
      }
      
      // TODO: Filter out booked slots by checking existing bookings
      
      return slots;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get available time slots');
    }
  }

  // Get technician categories
  getServiceCategories(): string[] {
    return [
      'Nails',
      'Lashes',
      'Brows',
      'Hair',
      'Makeup',
      'Skincare',
      'Massage',
      'Waxing',
    ];
  }

  // Format technician distance
  formatDistance(distanceMiles: number): string {
    if (distanceMiles < 1) {
      return `${Math.round(distanceMiles * 1760)} yards away`;
    }
    return `${distanceMiles.toFixed(1)} miles away`;
  }

  // Get verification status display
  getVerificationStatusDisplay(status: string): {
    text: string;
    color: string;
    icon: string;
  } {
    const statusMap: Record<string, { text: string; color: string; icon: string }> = {
      unverified: {
        text: 'Unverified',
        color: '#9E9E9E',
        icon: 'help-circle',
      },
      verified: {
        text: 'Verified',
        color: '#4CAF50',
        icon: 'check-circle',
      },
      certified: {
        text: 'Certified',
        color: '#2196F3',
        icon: 'award',
      },
    };
    
    return statusMap[status] || statusMap.unverified;
  }

  // Get availability status display
  getAvailabilityStatusDisplay(status: string): {
    text: string;
    color: string;
  } {
    const statusMap: Record<string, { text: string; color: string }> = {
      available: {
        text: 'Available',
        color: '#4CAF50',
      },
      unavailable: {
        text: 'Unavailable',
        color: '#F44336',
      },
    };
    
    return statusMap[status] || statusMap.unavailable;
  }

  // Calculate estimated travel time (placeholder)
  async getEstimatedTravelTime(
    fromLat: number,
    fromLng: number,
    toLat: number,
    toLng: number
  ): Promise<{ duration: string; distance: string }> {
    // This would integrate with Google Maps API in a real implementation
    // For now, return estimated values based on distance
    
    const distance = this.calculateDistance(fromLat, fromLng, toLat, toLng);
    const estimatedMinutes = Math.round(distance * 3); // Rough estimate: 3 minutes per mile
    
    return {
      duration: `${estimatedMinutes} min`,
      distance: this.formatDistance(distance),
    };
  }

  // Calculate distance between two points (Haversine formula)
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 3959; // Earth's radius in miles
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

export const technicianService = new TechnicianService();
export default technicianService;
