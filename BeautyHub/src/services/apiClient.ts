import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import { ENV, APP_CONFIG } from '../config/environment';

// Configuration - Now using Supabase as primary backend
const API_BASE_URL = ENV.SUPABASE_URL;
const API_TIMEOUT = APP_CONFIG.API_TIMEOUT;

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token from Supabase
    this.client.interceptors.request.use(
      async (config) => {
        try {
          const { data: { session } } = await supabase.auth.getSession();
          if (session?.access_token) {
            config.headers.Authorization = `Bearer ${session.access_token}`;
          }
        } catch (error) {
          console.error('Error getting Supabase session:', error);
        }
        
        // Log request in development
        if (__DEV__) {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params,
          });
        }
        
        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Log response in development
        if (__DEV__) {
          console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
            status: response.status,
            data: response.data,
          });
        }
        
        return response;
      },
      async (error) => {
        // Log error in development
        if (__DEV__) {
          console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
            status: error.response?.status,
            data: error.response?.data,
            message: error.message,
          });
        }

        // Handle 401 Unauthorized - token expired or invalid
        if (error.response?.status === 401) {
          try {
            await supabase.auth.signOut();
            // You might want to redirect to login screen here
            // This would require access to navigation, which we'll handle in the auth context
          } catch (authError) {
            console.error('Error signing out from Supabase:', authError);
          }
        }

        // Handle network errors
        if (!error.response) {
          error.message = 'Network error. Please check your internet connection.';
        }

        return Promise.reject(error);
      }
    );
  }

  // HTTP Methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch<T>(url, data, config);
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  // File upload method
  async uploadFile<T>(
    url: string, 
    file: FormData, 
    onUploadProgress?: (progressEvent: any) => void
  ): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
  }

  // Method to update base URL (useful for switching environments)
  updateBaseURL(newBaseURL: string) {
    this.client.defaults.baseURL = newBaseURL;
  }

  // Method to set custom headers
  setHeader(key: string, value: string) {
    this.client.defaults.headers.common[key] = value;
  }

  // Method to remove custom headers
  removeHeader(key: string) {
    delete this.client.defaults.headers.common[key];
  }

  // Method to get current base URL
  getBaseURL(): string {
    return this.client.defaults.baseURL || '';
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for testing purposes
export { ApiClient };
