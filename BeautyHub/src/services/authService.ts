import { User, LoginForm, RegisterForm } from '../types';
import { authHelpers, dbHelpers } from './supabase';
import { Session, AuthError } from '@supabase/supabase-js';

interface AuthResponse {
  user: User;
  session: Session;
}

class AuthService {
  async login(credentials: LoginForm): Promise<AuthResponse> {
    try {
      const { data, error } = await authHelpers.signIn(credentials.email, credentials.password);

      if (error) {
        throw new Error(error.message);
      }

      if (!data.session || !data.user) {
        throw new Error('Login failed - no session created');
      }

      // Get user profile from our users table
      const { data: userProfile, error: profileError } = await dbHelpers.getUser(data.user.id);

      if (profileError || !userProfile) {
        throw new Error('Failed to load user profile');
      }

      return {
        user: userProfile as User,
        session: data.session,
      };
    } catch (error: any) {
      throw new Error(error.message || 'Lo<PERSON> failed');
    }
  }

  async register(userData: RegisterForm): Promise<{ message: string }> {
    try {
      const { data, error } = await authHelpers.signUp(
        userData.email,
        userData.password,
        {
          name: userData.name,
          role: userData.role,
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      // Supabase will send a confirmation email
      return {
        message: 'Registration successful! Please check your email to verify your account.',
      };
    } catch (error: any) {
      throw new Error(error.message || 'Registration failed');
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const { user, error } = await authHelpers.getCurrentUser();

      if (error || !user) {
        return null;
      }

      // Get user profile from our users table
      const { data: userProfile, error: profileError } = await dbHelpers.getUser(user.id);

      if (profileError || !userProfile) {
        return null;
      }

      return userProfile as User;
    } catch (error) {
      return null;
    }
  }

  async forgotPassword(email: string): Promise<{ message: string }> {
    try {
      const { data, error } = await authHelpers.resetPassword(email);

      if (error) {
        throw new Error(error.message);
      }

      return {
        message: 'Password reset email sent! Please check your inbox.',
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send reset email');
    }
  }

  async updatePassword(newPassword: string): Promise<{ message: string }> {
    try {
      const { data, error } = await authHelpers.updatePassword(newPassword);

      if (error) {
        throw new Error(error.message);
      }

      return {
        message: 'Password updated successfully!',
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update password');
    }
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const { user } = await authHelpers.getCurrentUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await dbHelpers.updateUser(user.id, userData);

      if (error || !data) {
        throw new Error(error?.message || 'Failed to update profile');
      }

      return data as User;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update profile');
    }
  }

  async signOut(): Promise<void> {
    try {
      const { error } = await authHelpers.signOut();

      if (error) {
        throw new Error(error.message);
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign out');
    }
  }

  async getSession(): Promise<Session | null> {
    try {
      const { session, error } = await authHelpers.getSession();

      if (error) {
        console.error('Error getting session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return authHelpers.supabase.auth.onAuthStateChange(callback);
  }
}

export const authService = new AuthService();
