import { dbHelpers, realtimeHelpers } from './supabase';
import { Database } from '../types/database';

type Booking = Database['public']['Tables']['bookings']['Row'];
type BookingInsert = Database['public']['Tables']['bookings']['Insert'];
type BookingUpdate = Database['public']['Tables']['bookings']['Update'];

class BookingService {
  // Create a new booking
  async createBooking(bookingData: BookingInsert): Promise<Booking> {
    try {
      const { data, error } = await dbHelpers.createBooking(bookingData);
      
      if (error || !data) {
        throw new Error(error?.message || 'Failed to create booking');
      }
      
      return data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create booking');
    }
  }

  // Get user's bookings (customer or technician)
  async getUserBookings(userId: string, role: 'customer' | 'tech'): Promise<any[]> {
    try {
      const { data, error } = await dbHelpers.getUserBookings(userId, role);
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data || [];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch bookings');
    }
  }

  // Update booking status
  async updateBooking(bookingId: string, updates: BookingUpdate): Promise<Booking> {
    try {
      const { data, error } = await dbHelpers.updateBooking(bookingId, updates);
      
      if (error || !data) {
        throw new Error(error?.message || 'Failed to update booking');
      }
      
      return data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update booking');
    }
  }

  // Confirm booking (technician action)
  async confirmBooking(bookingId: string): Promise<Booking> {
    return this.updateBooking(bookingId, { status: 'confirmed' });
  }

  // Cancel booking
  async cancelBooking(bookingId: string): Promise<Booking> {
    return this.updateBooking(bookingId, { status: 'cancelled' });
  }

  // Complete booking
  async completeBooking(bookingId: string): Promise<Booking> {
    return this.updateBooking(bookingId, { status: 'completed' });
  }

  // Mark as no-show
  async markNoShow(bookingId: string): Promise<Booking> {
    return this.updateBooking(bookingId, { status: 'no_show' });
  }

  // Check booking availability
  async checkAvailability(
    techId: string,
    serviceId: string,
    appointmentTime: string
  ): Promise<boolean> {
    try {
      // This would use the database function we created
      const { data, error } = await dbHelpers.supabase.rpc('check_booking_availability', {
        tech_uuid: techId,
        service_uuid: serviceId,
        requested_time: appointmentTime,
      });
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data || false;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to check availability');
    }
  }

  // Get upcoming bookings for a user
  async getUpcomingBookings(userId: string, role: 'customer' | 'tech'): Promise<any[]> {
    try {
      const bookings = await this.getUserBookings(userId, role);
      const now = new Date();
      
      return bookings.filter(booking => {
        const appointmentTime = new Date(booking.appointment_time);
        return appointmentTime > now && booking.status !== 'cancelled';
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch upcoming bookings');
    }
  }

  // Get past bookings for a user
  async getPastBookings(userId: string, role: 'customer' | 'tech'): Promise<any[]> {
    try {
      const bookings = await this.getUserBookings(userId, role);
      const now = new Date();
      
      return bookings.filter(booking => {
        const appointmentTime = new Date(booking.appointment_time);
        return appointmentTime <= now || booking.status === 'completed';
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch past bookings');
    }
  }

  // Subscribe to booking updates
  subscribeToBookings(
    userId: string,
    role: 'customer' | 'tech',
    callback: (payload: any) => void
  ) {
    return realtimeHelpers.subscribeToBookings(userId, role, callback);
  }

  // Calculate booking total with deposit
  calculateBookingTotal(servicePricePence: number, depositPence: number): {
    total: number;
    deposit: number;
    remaining: number;
  } {
    return {
      total: servicePricePence,
      deposit: depositPence,
      remaining: servicePricePence - depositPence,
    };
  }

  // Format price for display
  formatPrice(pence: number): string {
    return `£${(pence / 100).toFixed(2)}`;
  }

  // Get booking status display text
  getStatusDisplayText(status: string): string {
    const statusMap: Record<string, string> = {
      pending: 'Pending Confirmation',
      confirmed: 'Confirmed',
      completed: 'Completed',
      cancelled: 'Cancelled',
      no_show: 'No Show',
    };
    
    return statusMap[status] || status;
  }

  // Get booking status color
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      pending: '#FFA500', // Orange
      confirmed: '#4CAF50', // Green
      completed: '#2196F3', // Blue
      cancelled: '#F44336', // Red
      no_show: '#9E9E9E', // Grey
    };
    
    return colorMap[status] || '#9E9E9E';
  }

  // Check if booking can be cancelled
  canCancelBooking(booking: Booking): boolean {
    const now = new Date();
    const appointmentTime = new Date(booking.appointment_time);
    const hoursUntilAppointment = (appointmentTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    // Can cancel if more than 24 hours before appointment and status is pending or confirmed
    return hoursUntilAppointment > 24 && ['pending', 'confirmed'].includes(booking.status);
  }

  // Check if booking can be modified
  canModifyBooking(booking: Booking): boolean {
    const now = new Date();
    const appointmentTime = new Date(booking.appointment_time);
    const hoursUntilAppointment = (appointmentTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    // Can modify if more than 48 hours before appointment and status is pending or confirmed
    return hoursUntilAppointment > 48 && ['pending', 'confirmed'].includes(booking.status);
  }
}

export const bookingService = new BookingService();
export default bookingService;
