import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Database } from '../types/database';

// Environment variables - these should be set in your .env file
const supabaseUrl = process.env.SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'your_anon_key_here';

// Create Supabase client with AsyncStorage for session persistence
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Auth helper functions
export const authHelpers = {
  // Sign up with email and password
  signUp: async (email: string, password: string, userData: { name: string; role: 'customer' | 'tech' }) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData,
      },
    });
    return { data, error };
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // Get current session
  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    return { session, error };
  },

  // Get current user
  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  // Reset password
  resetPassword: async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email);
    return { data, error };
  },

  // Update password
  updatePassword: async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({ password });
    return { data, error };
  },
};

// Database helper functions
export const dbHelpers = {
  // Users
  getUser: async (userId: string) => {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('user_id', userId)
      .single();
    return { data, error };
  },

  updateUser: async (userId: string, updates: Partial<Database['public']['Tables']['users']['Update']>) => {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();
    return { data, error };
  },

  // Beauty Technicians
  getTechnician: async (techId: string) => {
    const { data, error } = await supabase
      .from('beauty_technicians')
      .select(`
        *,
        users (name, email, profile_picture_url)
      `)
      .eq('tech_id', techId)
      .single();
    return { data, error };
  },

  getNearbyTechnicians: async (lat: number, lng: number, radiusMiles: number = 25) => {
    const { data, error } = await supabase.rpc('find_nearby_technicians', {
      user_lat: lat,
      user_lng: lng,
      radius_miles: radiusMiles,
    });
    return { data, error };
  },

  // Services
  getServices: async (techId?: string, category?: string) => {
    let query = supabase
      .from('services')
      .select(`
        *,
        beauty_technicians (
          studio_name,
          users (name, profile_picture_url)
        )
      `)
      .eq('is_active', true);

    if (techId) {
      query = query.eq('tech_id', techId);
    }

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    return { data, error };
  },

  getService: async (serviceId: string) => {
    const { data, error } = await supabase
      .from('services')
      .select(`
        *,
        beauty_technicians (
          studio_name,
          location,
          users (name, profile_picture_url)
        )
      `)
      .eq('service_id', serviceId)
      .single();
    return { data, error };
  },

  // Bookings
  createBooking: async (booking: Database['public']['Tables']['bookings']['Insert']) => {
    const { data, error } = await supabase
      .from('bookings')
      .insert(booking)
      .select()
      .single();
    return { data, error };
  },

  getUserBookings: async (userId: string, role: 'customer' | 'tech') => {
    const column = role === 'customer' ? 'customer_id' : 'tech_id';
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        services (name, duration_minutes, price_pence),
        users!customer_id (name, email, profile_picture_url),
        beauty_technicians (
          studio_name,
          users (name, profile_picture_url)
        )
      `)
      .eq(column, userId)
      .order('appointment_time', { ascending: false });
    return { data, error };
  },

  updateBooking: async (bookingId: string, updates: Database['public']['Tables']['bookings']['Update']) => {
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('booking_id', bookingId)
      .select()
      .single();
    return { data, error };
  },

  // Reviews
  createReview: async (review: Database['public']['Tables']['reviews']['Insert']) => {
    const { data, error } = await supabase
      .from('reviews')
      .insert(review)
      .select()
      .single();
    return { data, error };
  },

  getTechnicianReviews: async (techId: string) => {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        users!reviewer_id (name, profile_picture_url)
      `)
      .eq('reviewee_id', techId)
      .order('created_at', { ascending: false });
    return { data, error };
  },

  // Notifications
  getUserNotifications: async (userId: string) => {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    return { data, error };
  },

  markNotificationAsRead: async (notificationId: string) => {
    const { data, error } = await supabase
      .from('notifications')
      .update({ read_status: true })
      .eq('notification_id', notificationId);
    return { data, error };
  },
};

// Storage helper functions
export const storageHelpers = {
  uploadFile: async (bucket: string, path: string, file: File | Blob) => {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file);
    return { data, error };
  },

  getPublicUrl: (bucket: string, path: string) => {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);
    return data.publicUrl;
  },

  deleteFile: async (bucket: string, path: string) => {
    const { data, error } = await supabase.storage
      .from(bucket)
      .remove([path]);
    return { data, error };
  },
};

// Realtime subscriptions
export const realtimeHelpers = {
  subscribeToBookings: (userId: string, role: 'customer' | 'tech', callback: (payload: any) => void) => {
    const column = role === 'customer' ? 'customer_id' : 'tech_id';
    return supabase
      .channel('bookings')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings',
          filter: `${column}=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToNotifications: (userId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },
};

export default supabase;
