// Database types generated from Supabase schema
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          user_id: string
          role: 'customer' | 'tech' | 'admin'
          name: string
          email: string
          phone_number: string | null
          profile_picture_url: string | null
          language_preference: string | null
          timezone: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          role?: 'customer' | 'tech' | 'admin'
          name: string
          email: string
          phone_number?: string | null
          profile_picture_url?: string | null
          language_preference?: string | null
          timezone?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          user_id?: string
          role?: 'customer' | 'tech' | 'admin'
          name?: string
          email?: string
          phone_number?: string | null
          profile_picture_url?: string | null
          language_preference?: string | null
          timezone?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      beauty_technicians: {
        Row: {
          tech_id: string
          studio_name: string | null
          location: unknown | null // PostGIS geography type
          header_image_url: string | null
          bio: string | null
          social_links: Json
          working_hours: Json
          availability_status: 'available' | 'unavailable'
          verification_status: 'unverified' | 'verified' | 'certified'
          certifications: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          tech_id: string
          studio_name?: string | null
          location?: unknown | null
          header_image_url?: string | null
          bio?: string | null
          social_links?: Json
          working_hours?: Json
          availability_status?: 'available' | 'unavailable'
          verification_status?: 'unverified' | 'verified' | 'certified'
          certifications?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          tech_id?: string
          studio_name?: string | null
          location?: unknown | null
          header_image_url?: string | null
          bio?: string | null
          social_links?: Json
          working_hours?: Json
          availability_status?: 'available' | 'unavailable'
          verification_status?: 'unverified' | 'verified' | 'certified'
          certifications?: Json
          created_at?: string
          updated_at?: string
        }
      }
      services: {
        Row: {
          service_id: string
          tech_id: string
          name: string
          description: string | null
          category: string | null
          duration_minutes: number
          price_pence: number
          deposit_pence: number
          add_ons: Json
          faqs: Json
          images: Json
          video_urls: Json
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          service_id?: string
          tech_id: string
          name: string
          description?: string | null
          category?: string | null
          duration_minutes: number
          price_pence: number
          deposit_pence: number
          add_ons?: Json
          faqs?: Json
          images?: Json
          video_urls?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          service_id?: string
          tech_id?: string
          name?: string
          description?: string | null
          category?: string | null
          duration_minutes?: number
          price_pence?: number
          deposit_pence?: number
          add_ons?: Json
          faqs?: Json
          images?: Json
          video_urls?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      bookings: {
        Row: {
          booking_id: string
          customer_id: string
          service_id: string
          tech_id: string
          status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'no_show'
          appointment_time: string
          deposit_paid: boolean
          auto_release_time: string | null
          total_amount_pence: number
          payment_status: 'pending' | 'paid' | 'refunded'
          created_at: string
          updated_at: string
        }
        Insert: {
          booking_id?: string
          customer_id: string
          service_id: string
          tech_id: string
          status?: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'no_show'
          appointment_time: string
          deposit_paid?: boolean
          auto_release_time?: string | null
          total_amount_pence: number
          payment_status?: 'pending' | 'paid' | 'refunded'
          created_at?: string
          updated_at?: string
        }
        Update: {
          booking_id?: string
          customer_id?: string
          service_id?: string
          tech_id?: string
          status?: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'no_show'
          appointment_time?: string
          deposit_paid?: boolean
          auto_release_time?: string | null
          total_amount_pence?: number
          payment_status?: 'pending' | 'paid' | 'refunded'
          created_at?: string
          updated_at?: string
        }
      }
      reviews: {
        Row: {
          review_id: string
          booking_id: string
          reviewer_id: string
          reviewee_id: string
          rating: number
          comment: string | null
          media: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          review_id?: string
          booking_id: string
          reviewer_id: string
          reviewee_id: string
          rating: number
          comment?: string | null
          media?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          review_id?: string
          booking_id?: string
          reviewer_id?: string
          reviewee_id?: string
          rating?: number
          comment?: string | null
          media?: Json
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          notification_id: string
          user_id: string
          type: 'booking' | 'cancellation' | 'reminder' | 'marketing'
          message: string
          read_status: boolean
          created_at: string
        }
        Insert: {
          notification_id?: string
          user_id: string
          type: 'booking' | 'cancellation' | 'reminder' | 'marketing'
          message: string
          read_status?: boolean
          created_at?: string
        }
        Update: {
          notification_id?: string
          user_id?: string
          type?: 'booking' | 'cancellation' | 'reminder' | 'marketing'
          message?: string
          read_status?: boolean
          created_at?: string
        }
      }
      account_settings: {
        Row: {
          settings_id: string
          user_id: string
          linked_devices: Json
          communication_preferences: Json
          privacy_settings: Json
          dark_mode: boolean
          accessibility_options: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          settings_id?: string
          user_id: string
          linked_devices?: Json
          communication_preferences?: Json
          privacy_settings?: Json
          dark_mode?: boolean
          accessibility_options?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          settings_id?: string
          user_id?: string
          linked_devices?: Json
          communication_preferences?: Json
          privacy_settings?: Json
          dark_mode?: boolean
          accessibility_options?: Json
          created_at?: string
          updated_at?: string
        }
      }
      payment_accounts: {
        Row: {
          payment_id: string
          tech_id: string
          stripe_account_id: string | null
          payout_method: 'instant' | 'weekly' | 'monthly'
          created_at: string
          updated_at: string
        }
        Insert: {
          payment_id?: string
          tech_id: string
          stripe_account_id?: string | null
          payout_method?: 'instant' | 'weekly' | 'monthly'
          created_at?: string
          updated_at?: string
        }
        Update: {
          payment_id?: string
          tech_id?: string
          stripe_account_id?: string | null
          payout_method?: 'instant' | 'weekly' | 'monthly'
          created_at?: string
          updated_at?: string
        }
      }
      audit_logs: {
        Row: {
          log_id: string
          user_id: string | null
          action_type: string
          action_details: Json
          created_at: string
        }
        Insert: {
          log_id?: string
          user_id?: string | null
          action_type: string
          action_details?: Json
          created_at?: string
        }
        Update: {
          log_id?: string
          user_id?: string | null
          action_type?: string
          action_details?: Json
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      find_nearby_technicians: {
        Args: {
          user_lat: number
          user_lng: number
          radius_miles?: number
        }
        Returns: {
          tech_id: string
          studio_name: string
          distance_miles: number
          verification_status: 'unverified' | 'verified' | 'certified'
          availability_status: 'available' | 'unavailable'
        }[]
      }
      get_tech_average_rating: {
        Args: {
          tech_uuid: string
        }
        Returns: number
      }
      get_tech_review_count: {
        Args: {
          tech_uuid: string
        }
        Returns: number
      }
      check_booking_availability: {
        Args: {
          tech_uuid: string
          service_uuid: string
          requested_time: string
        }
        Returns: boolean
      }
    }
    Enums: {
      user_role: 'customer' | 'tech' | 'admin'
      availability_status: 'available' | 'unavailable'
      verification_status: 'unverified' | 'verified' | 'certified'
      booking_status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'no_show'
      payment_status: 'pending' | 'paid' | 'refunded'
      notification_type: 'booking' | 'cancellation' | 'reminder' | 'marketing'
      payout_method: 'instant' | 'weekly' | 'monthly'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
