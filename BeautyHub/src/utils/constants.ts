/**
 * App constants for BeautyHub
 */

// Colors
export const COLORS = {
  primary: '#FF6B9D',
  secondary: '#9B59B6',
  accent: '#3498DB',
  success: '#2ECC71',
  warning: '#F39C12',
  error: '#E74C3C',
  info: '#3498DB',
  
  // Grays
  dark: '#2C3E50',
  medium: '#7F8C8D',
  light: '#BDC3C7',
  lighter: '#ECF0F1',
  white: '#FFFFFF',
  
  // Background
  background: '#FFFFFF',
  surface: '#F8F9FA',
  border: '#E8E8E8',
  
  // Text
  textPrimary: '#2C3E50',
  textSecondary: '#7F8C8D',
  textLight: '#BDC3C7',
} as const;

// Typography
export const FONTS = {
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
    huge: 32,
  },
  weights: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
} as const;

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  huge: 40,
} as const;

// Border Radius
export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  round: 50,
} as const;

// Service Categories
export const SERVICE_CATEGORIES = [
  { id: 'nails', name: 'Nails', icon: 'hand-left-outline' },
  { id: 'hair', name: 'Hair', icon: 'cut-outline' },
  { id: 'makeup', name: 'Makeup', icon: 'color-palette-outline' },
  { id: 'skincare', name: 'Skincare', icon: 'flower-outline' },
  { id: 'lashes', name: 'Lashes', icon: 'eye-outline' },
  { id: 'brows', name: 'Brows', icon: 'remove-outline' },
  { id: 'massage', name: 'Massage', icon: 'hand-right-outline' },
  { id: 'other', name: 'Other', icon: 'ellipsis-horizontal-outline' },
] as const;

// Booking Status Colors
export const BOOKING_STATUS_COLORS = {
  pending: COLORS.warning,
  confirmed: COLORS.info,
  in_progress: COLORS.primary,
  completed: COLORS.success,
  cancelled: COLORS.error,
  no_show: COLORS.medium,
} as const;

// Payment Method Icons
export const PAYMENT_METHOD_ICONS = {
  card: 'card-outline',
  apple_pay: 'logo-apple',
  google_pay: 'logo-google',
} as const;

// App Configuration
export const APP_CONFIG = {
  name: 'BeautyHub',
  version: '1.0.0',
  supportEmail: '<EMAIL>',
  privacyPolicyUrl: 'https://beautyhub.com/privacy',
  termsOfServiceUrl: 'https://beautyhub.com/terms',
  
  // API Configuration
  api: {
    timeout: 10000,
    retryAttempts: 3,
  },
  
  // Map Configuration
  map: {
    defaultRadius: 25, // miles
    maxRadius: 100,
    minRadius: 5,
  },
  
  // Booking Configuration
  booking: {
    maxAdvanceBookingDays: 90,
    minAdvanceBookingHours: 2,
    cancellationWindowHours: 24,
  },
  
  // File Upload
  upload: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxImages: 5,
  },
} as const;

// Animation Durations
export const ANIMATION_DURATION = {
  fast: 200,
  normal: 300,
  slow: 500,
} as const;

// Screen Dimensions Breakpoints
export const BREAKPOINTS = {
  small: 320,
  medium: 768,
  large: 1024,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  network: 'Network error. Please check your connection.',
  unauthorized: 'Session expired. Please log in again.',
  forbidden: 'You do not have permission to perform this action.',
  notFound: 'The requested resource was not found.',
  serverError: 'Server error. Please try again later.',
  validation: 'Please check your input and try again.',
  unknown: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  login: 'Welcome back!',
  register: 'Account created successfully!',
  logout: 'Logged out successfully.',
  profileUpdated: 'Profile updated successfully.',
  bookingCreated: 'Booking created successfully!',
  bookingCancelled: 'Booking cancelled successfully.',
  paymentProcessed: 'Payment processed successfully.',
  reviewSubmitted: 'Review submitted successfully.',
} as const;
