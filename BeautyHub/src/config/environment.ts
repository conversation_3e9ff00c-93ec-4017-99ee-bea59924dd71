import { Platform } from 'react-native';

// Environment configuration
export const ENV = {
  // App Environment
  NODE_ENV: process.env.NODE_ENV || 'development',
  APP_ENV: process.env.APP_ENV || 'development',
  
  // Supabase Configuration
  SUPABASE_URL: process.env.SUPABASE_URL || 'http://127.0.0.1:54321',
  SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
  
  // Local development URLs (when using supabase start)
  SUPABASE_LOCAL_URL: 'http://127.0.0.1:54321',
  SUPABASE_LOCAL_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
  
  // API Configuration
  API_BASE_URL: process.env.API_BASE_URL || 'http://127.0.0.1:54321',
  WEB_URL: process.env.WEB_URL || 'http://localhost:3000',
  
  // External Services
  STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY || '',
  GOOGLE_MAPS_API_KEY: process.env.GOOGLE_MAPS_API_KEY || '',
  
  // Firebase Configuration
  FCM_SENDER_ID: process.env.FCM_SENDER_ID || '',
  
  // Development flags
  FLIPPER_DISABLE: process.env.FLIPPER_DISABLE === 'true',
  
  // Platform specific configurations
  IS_IOS: Platform.OS === 'ios',
  IS_ANDROID: Platform.OS === 'android',
  IS_DEV: __DEV__,
};

// Validate required environment variables
export const validateEnvironment = () => {
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
  ];
  
  const missingVars = requiredVars.filter(varName => !ENV[varName as keyof typeof ENV]);
  
  if (missingVars.length > 0) {
    console.warn('Missing environment variables:', missingVars);
    if (ENV.APP_ENV === 'production') {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
  }
};

// App configuration based on environment
export const APP_CONFIG = {
  // API timeouts
  API_TIMEOUT: ENV.APP_ENV === 'development' ? 30000 : 10000,
  
  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  
  // Cache configuration
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  
  // Location configuration
  LOCATION_TIMEOUT: 15000,
  LOCATION_MAX_AGE: 5 * 60 * 1000, // 5 minutes
  
  // Image upload configuration
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  
  // Booking configuration
  MIN_BOOKING_ADVANCE_HOURS: 2,
  MAX_BOOKING_ADVANCE_DAYS: 90,
  CANCELLATION_WINDOW_HOURS: 24,
  
  // Search configuration
  DEFAULT_SEARCH_RADIUS: 25, // miles
  MAX_SEARCH_RADIUS: 100,
  MIN_SEARCH_RADIUS: 5,
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Feature flags
  FEATURES: {
    REALTIME_NOTIFICATIONS: true,
    LOCATION_SERVICES: true,
    PUSH_NOTIFICATIONS: true,
    BIOMETRIC_AUTH: Platform.OS === 'ios' || Platform.OS === 'android',
    DARK_MODE: true,
    OFFLINE_MODE: false, // Future feature
  },
};

// Supabase configuration
export const SUPABASE_CONFIG = {
  url: ENV.APP_ENV === 'development' ? ENV.SUPABASE_LOCAL_URL : ENV.SUPABASE_URL,
  anonKey: ENV.APP_ENV === 'development' ? ENV.SUPABASE_LOCAL_ANON_KEY : ENV.SUPABASE_ANON_KEY,
  
  // Storage buckets
  STORAGE_BUCKETS: {
    PROFILE_IMAGES: 'profile-images',
    SERVICE_IMAGES: 'service-images',
    HEADER_IMAGES: 'header-images',
    CERTIFICATIONS: 'certifications',
    REVIEW_MEDIA: 'review-media',
  },
  
  // Realtime channels
  REALTIME_CHANNELS: {
    BOOKINGS: 'bookings',
    NOTIFICATIONS: 'notifications',
    AVAILABILITY: 'availability',
  },
};

// Stripe configuration
export const STRIPE_CONFIG = {
  publishableKey: ENV.STRIPE_PUBLISHABLE_KEY,
  merchantIdentifier: 'merchant.com.beautyhub.app',
  urlScheme: 'beautyhub',
  
  // Payment configuration
  CURRENCY: 'gbp',
  COUNTRY: 'GB',
  
  // Minimum amounts (in pence)
  MIN_DEPOSIT_AMOUNT: 500, // £5.00
  MIN_SERVICE_AMOUNT: 1000, // £10.00
};

// Google Maps configuration
export const MAPS_CONFIG = {
  apiKey: ENV.GOOGLE_MAPS_API_KEY,
  
  // Default map settings
  DEFAULT_REGION: {
    latitude: 51.5074, // London
    longitude: -0.1278,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  
  // Map styling
  MAP_STYLE: ENV.APP_ENV === 'development' ? [] : [], // Add custom styles for production
};

// Logging configuration
export const LOGGING_CONFIG = {
  ENABLED: ENV.APP_ENV !== 'production',
  LEVEL: ENV.APP_ENV === 'development' ? 'debug' : 'error',
  
  // Remote logging (for production)
  REMOTE_LOGGING: ENV.APP_ENV === 'production',
};

// Initialize environment validation
if (ENV.APP_ENV !== 'test') {
  validateEnvironment();
}

export default {
  ENV,
  APP_CONFIG,
  SUPABASE_CONFIG,
  STRIPE_CONFIG,
  MAPS_CONFIG,
  LOGGING_CONFIG,
};
