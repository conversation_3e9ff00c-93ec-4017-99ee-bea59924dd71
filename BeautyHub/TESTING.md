# Testing Guide for BeautyHub

This document outlines the testing strategy and guidelines for the BeautyHub React Native application.

## Testing Stack

- **Jest**: JavaScript testing framework
- **React Native Testing Library**: Testing utilities for React Native components
- **Detox**: End-to-end testing framework (future implementation)
- **ESLint**: Code linting and style checking
- **TypeScript**: Static type checking

## Test Structure

```
BeautyHub/
├── __tests__/
│   ├── components/          # Component tests
│   ├── context/            # Context provider tests
│   ├── utils/              # Utility function tests
│   ├── services/           # API service tests
│   └── screens/            # Screen component tests
├── src/
│   └── **/__tests__/       # Co-located tests
├── jest.config.js          # Jest configuration
├── jest.setup.js           # Test setup and mocks
└── TESTING.md             # This file
```

## Running Tests

### Unit Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- ValidationUtils.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="validation"
```

### Linting
```bash
# Run ESLint
npm run lint

# Fix linting errors automatically
npm run lint:fix
```

### Type Checking
```bash
# Run TypeScript compiler check
npx tsc --noEmit
```

## Testing Guidelines

### 1. Unit Tests

**What to Test:**
- Utility functions
- Custom hooks
- Component logic
- Context providers
- Service functions

**Example:**
```typescript
// utils/validation.test.ts
import { validateEmail } from '../validation';

describe('validateEmail', () => {
  it('should validate correct email addresses', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('should reject invalid email addresses', () => {
    expect(validateEmail('invalid-email')).toBe(false);
  });
});
```

### 2. Component Tests

**What to Test:**
- Component rendering
- User interactions
- Props handling
- State changes

**Example:**
```typescript
// components/LoadingSpinner.test.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import LoadingSpinner from '../LoadingSpinner';

describe('LoadingSpinner', () => {
  it('renders with custom text', () => {
    const { getByText } = render(<LoadingSpinner text="Loading..." />);
    expect(getByText('Loading...')).toBeTruthy();
  });
});
```

### 3. Integration Tests

**What to Test:**
- Screen navigation
- Context integration
- API integration
- Form submissions

### 4. Mocking Strategy

**Common Mocks:**
- AsyncStorage
- Navigation
- Vector Icons
- API calls
- External libraries

**Mock Location:**
- Global mocks: `jest.setup.js`
- Test-specific mocks: Individual test files

## Test Categories

### Critical Path Tests (Priority 1)
- [ ] User authentication flow
- [ ] Service booking process
- [ ] Payment processing
- [ ] Profile management
- [ ] Core navigation

### Feature Tests (Priority 2)
- [ ] Search functionality
- [ ] Review system
- [ ] Notification handling
- [ ] Working hours management
- [ ] Service management

### Edge Case Tests (Priority 3)
- [ ] Network error handling
- [ ] Offline functionality
- [ ] Input validation
- [ ] Permission handling
- [ ] Deep linking

## Coverage Goals

- **Statements**: > 80%
- **Branches**: > 75%
- **Functions**: > 80%
- **Lines**: > 80%

## Best Practices

### 1. Test Naming
```typescript
describe('ComponentName', () => {
  describe('when condition', () => {
    it('should do something', () => {
      // Test implementation
    });
  });
});
```

### 2. Arrange-Act-Assert Pattern
```typescript
it('should calculate total price correctly', () => {
  // Arrange
  const services = [{ price: 25 }, { price: 35 }];
  
  // Act
  const total = calculateTotal(services);
  
  // Assert
  expect(total).toBe(60);
});
```

### 3. Test Data
- Use factories for complex test data
- Keep test data minimal and focused
- Use meaningful test data that reflects real usage

### 4. Async Testing
```typescript
it('should fetch user data', async () => {
  const userData = await fetchUser('123');
  expect(userData.id).toBe('123');
});
```

### 5. Error Testing
```typescript
it('should handle network errors gracefully', async () => {
  mockApiCall.mockRejectedValue(new Error('Network error'));
  
  await expect(fetchData()).rejects.toThrow('Network error');
});
```

## Continuous Integration

### Pre-commit Hooks
- Run linting
- Run type checking
- Run affected tests

### CI Pipeline
1. Install dependencies
2. Run linting
3. Run type checking
4. Run all tests with coverage
5. Upload coverage reports
6. Build application

## Future Enhancements

### E2E Testing with Detox
```bash
# Install Detox
npm install --save-dev detox

# Run E2E tests
npm run e2e:ios
npm run e2e:android
```

### Visual Regression Testing
- Implement screenshot testing
- Compare UI changes across builds
- Automated visual testing in CI

### Performance Testing
- Bundle size monitoring
- Runtime performance testing
- Memory leak detection

## Debugging Tests

### Common Issues
1. **Mock not working**: Check mock setup in jest.setup.js
2. **Async test failing**: Ensure proper async/await usage
3. **Component not rendering**: Check for missing providers
4. **Navigation mock issues**: Verify navigation mock setup

### Debug Commands
```bash
# Run tests with verbose output
npm test -- --verbose

# Run single test file with debugging
npm test -- --runInBand ComponentName.test.tsx

# Debug with Node inspector
node --inspect-brk node_modules/.bin/jest --runInBand
```

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Native Testing Library](https://callstack.github.io/react-native-testing-library/)
- [Testing React Native Apps](https://reactnative.dev/docs/testing-overview)
- [Detox Documentation](https://github.com/wix/Detox)
