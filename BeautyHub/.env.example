# BeautyHub Environment Variables
# Copy this file to .env and fill in your actual values

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Local Development (when using supabase start)
SUPABASE_LOCAL_URL=http://127.0.0.1:54321
SUPABASE_LOCAL_ANON_KEY=your_local_anon_key

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Google Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Firebase Cloud Messaging
FCM_SERVER_KEY=your_fcm_server_key
FCM_SENDER_ID=your_fcm_sender_id

# App Configuration
APP_ENV=development
API_BASE_URL=http://127.0.0.1:54321
WEB_URL=http://localhost:3000

# Security
JWT_SECRET=your_jwt_secret_for_additional_tokens
ENCRYPTION_KEY=your_32_character_encryption_key

# External Services
OPENAI_API_KEY=your_openai_api_key_for_ai_features

# Development Tools
FLIPPER_DISABLE=true
REACT_NATIVE_PACKAGER_HOSTNAME=localhost
